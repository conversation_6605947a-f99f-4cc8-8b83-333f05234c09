# Import necessary libraries
import open3d as o3d
import numpy as np
from scipy.spatial import ConvexHull
import copy # To copy geometry for visualization
import os   # To handle file paths
import matplotlib.pyplot as plt
import cv2  # For minimum area rectangle calculation

def calculate_gripping_pose_base(ply_file_path,
                                rim_height_threshold_factor=0.05, # Adjusted default
                                visualize=True,
                                output_path=None,
                                camera_extrinsic=None):
    """
    Calculates the gripping pose (position and orientation) for the center
    of the longest top edge of a basket from a point cloud file, using
    world coordinates (where Z-axis is perpendicular to the ground) for filtering
    the upper edge point cloud.

    Args:
        ply_file_path (str or o3d.geometry.PointCloud): Path to the input PLY point cloud file or a PointCloud object.
        rim_height_threshold_factor (float): Percentage of the height extent
                                             used to define the rim thickness in the
                                             world coordinate system.
                                             Adjust based on point cloud density/noise.
        visualize (bool): If True, displays the point cloud, OBB, rim, edge,
                          and gripping pose using Open3D's interactive window.
        output_path (str, optional): Directory to save visualization outputs. Defaults to None.
        camera_extrinsic (np.ndarray, optional): 4x4 extrinsic matrix to transform from camera to world coordinates.
                                                If None, assumes the point cloud is already in world coordinates.

    Returns:
        tuple: A tuple containing:
            - gripping_point (np.ndarray): The 3D coordinates (x, y, z) of the
                                           calculated gripping point in the world frame.
                                           Returns None if processing fails.
            - rotation_matrix (np.ndarray): A 3x3 rotation matrix representing the
                                            gripper's orientation at the gripping point
                                            in the world frame.
                                            Returns None if processing fails.
            - target_edge_points (np.ndarray): The 3D coordinates of the two endpoints
                                               of the identified target edge in the world frame.
                                               Returns None if processing fails.
    """
    print(f"Loading point cloud from: {ply_file_path}")
    try:
        # 1. Load Point Cloud
        if isinstance(ply_file_path, o3d.geometry.PointCloud):
            pcd = ply_file_path
            print("Using provided PointCloud object; skipping file read.")
        else:
            pcd = o3d.io.read_point_cloud(ply_file_path)
            if not pcd.has_points():
                print("Error: Point cloud is empty or could not be read.")
                return None, None, None

        print(f"Loaded point cloud with {len(pcd.points)} points.")
        pcd_orig_for_vis = copy.deepcopy(pcd) # Keep original for final visualization

        # --- Optional Preprocessing ---
        # Consider applying preprocessing before further processing if needed
        pcd = pcd.voxel_down_sample(voxel_size=0.005)
        pcd, ind = pcd.remove_statistical_outlier(nb_neighbors=50, std_ratio=1.5)
        # --- End Optional Preprocessing ---

    except Exception as e:
        print(f"Error loading or preprocessing point cloud: {e}")
        return None, None, None

    try:
        # 2. Transform to world coordinates if camera_extrinsic is provided
        if camera_extrinsic is not None:
            print("Transforming point cloud from camera to world coordinates...")
            pcd_world = copy.deepcopy(pcd)
            # Apply extrinsic transformation
            pcd_world.transform(camera_extrinsic)
        else:
            print("Using point cloud as-is (assuming already in world coordinates)...")
            pcd_world = pcd

        # 3. Calculate Oriented Bounding Box (OBB) for visualization and size reference
        print("Calculating Oriented Bounding Box (OBB)...")
        obb = pcd_world.get_oriented_bounding_box()
        obb.color = [0, 1, 0] # Green color for OBB visualization
        obb_center = obb.center
        obb_rotation = obb.R # Rotation matrix from world to OBB axes
        obb_extent = obb.extent # Dimensions along OBB axes (x, y, z)
        print(f"OBB Center: {obb_center}")
        print(f"OBB Extent (dimensions): {obb_extent}")

        # 4. Isolate Upper Rim Points (in World Coordinates)
        # In world coordinates, Z-axis is perpendicular to the ground
        world_points = np.asarray(pcd_world.points)

        # Get the height range in world coordinates
        min_z_world = np.min(world_points[:, 2])  # Z is index 2 in world coordinates
        max_z_world = np.max(world_points[:, 2])
        height_range = max_z_world - min_z_world

        # Use height threshold to filter upper rim points
        rim_threshold_value = max_z_world - height_range * rim_height_threshold_factor
        rim_indices_world = np.where(world_points[:, 2] >= rim_threshold_value)[0]

        if len(rim_indices_world) < 3:
            print(f"Error: Not enough points found in the upper rim in world coordinates (found {len(rim_indices_world)}). Adjust rim_height_threshold_factor or check point cloud.")
            return None, None, None

        rim_points_3d_world = world_points[rim_indices_world]
        rim_pcd_world = o3d.geometry.PointCloud()
        rim_pcd_world.points = o3d.utility.Vector3dVector(rim_points_3d_world)
        rim_pcd_world.paint_uniform_color([1, 0, 0]) # Red color for rim points
        print(f"Isolated {len(rim_points_3d_world)} rim points in world coordinates.")

        # 5. Identify Target Edge
        # Project rim points onto the XY plane (perpendicular to world Z-axis)
        rim_points_2d_world = rim_points_3d_world[:, 0:2]  # Take X and Y coordinates

        # Find grasp points using minimum area rectangle
        point1_2d, point2_2d, box = find_grasp_points_min_area_rect(rim_points_2d_world)

        # Reconstruct 3D world coordinates by adding Z height
        # Find the appropriate Z height for each endpoint
        tol = 1e-3
        mask1 = np.isclose(rim_points_2d_world[:, 0], point1_2d[0], atol=tol) & np.isclose(rim_points_2d_world[:, 1], point1_2d[1], atol=tol)
        heights1 = rim_points_3d_world[mask1, 2]  # Z coordinate in world frame
        if heights1.size > 0:
            z1 = heights1.max()
        else:
            idx_nn1 = np.argmin(np.linalg.norm(rim_points_2d_world - point1_2d, axis=1))
            z1 = rim_points_3d_world[idx_nn1, 2]

        mask2 = np.isclose(rim_points_2d_world[:, 0], point2_2d[0], atol=tol) & np.isclose(rim_points_2d_world[:, 1], point2_2d[1], atol=tol)
        heights2 = rim_points_3d_world[mask2, 2]
        if heights2.size > 0:
            z2 = heights2.max()
        else:
            idx_nn2 = np.argmin(np.linalg.norm(rim_points_2d_world - point2_2d, axis=1))
            z2 = rim_points_3d_world[idx_nn2, 2]

        # Create 3D points for the target edge
        target_edge_p1_world = np.array([point1_2d[0], point1_2d[1], z1])
        target_edge_p2_world = np.array([point2_2d[0], point2_2d[1], z2])

        # Calculate edge length
        edge_len_world = np.linalg.norm(target_edge_p1_world - target_edge_p2_world)
        print(f"Identified edge with length: {edge_len_world:.4f}")

        # 6. Calculate Gripping Point
        gripping_point = (target_edge_p1_world + target_edge_p2_world) / 2.0
        print(f"Calculated Gripping Point (World): {gripping_point}")

        # 7. Determine Orientation
        # Edge vector (X-axis of gripper)
        edge_vector = target_edge_p2_world - target_edge_p1_world
        x_axis = edge_vector / np.linalg.norm(edge_vector)

        # Z-axis points downward (approach direction)
        z_axis = np.array([0, 0, -1])  # Negative Z in world coordinates

        # Y-axis is cross product of Z and X
        y_axis = np.cross(z_axis, x_axis)
        y_axis = y_axis / np.linalg.norm(y_axis)

        # Recalculate Z to ensure orthogonality
        z_axis = np.cross(x_axis, y_axis)
        z_axis = z_axis / np.linalg.norm(z_axis)

        # Create rotation matrix
        rotation_matrix = np.stack([x_axis, y_axis, z_axis], axis=1)
        print("Calculated Rotation Matrix (World):")
        print(rotation_matrix)

        # 8. Prepare target edge points for return
        target_edge_points = np.array([target_edge_p1_world, target_edge_p2_world])

        # --- Prepare geometries for visualization ---
        gripper_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.05, origin=[0, 0, 0])
        # Apply the rotation and translation
        gripper_frame.rotate(rotation_matrix, center=[0, 0, 0])
        gripper_frame.translate(gripping_point)

        start_sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.005).paint_uniform_color([0, 1, 0]).translate(target_edge_p1_world)
        end_sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.005).paint_uniform_color([0, 0, 1]).translate(target_edge_p2_world)
        grip_sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.007).paint_uniform_color([1, 1, 0]).translate(gripping_point)

        target_edge_line = o3d.geometry.LineSet(
            points=o3d.utility.Vector3dVector(target_edge_points),
            lines=o3d.utility.Vector2iVector([[0, 1]])
        )
        target_edge_line.colors = o3d.utility.Vector3dVector([[1, 1, 0]]) # Yellow line

        # Use original point cloud for display
        display_pcd = pcd_world.paint_uniform_color([0.5, 0.5, 0.5])

        # Create world coordinate frame for visualization (origin of the pcd_world space)
        world_frame_visualization = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1, origin=[0, 0, 0])
        # assign a color if you want, e.g.:
        world_frame_visualization.paint_uniform_color([0.2, 0.2, 0.8]) # Bluish for world

        # Determine the transformation for visualizing the camera's coordinate system pose in the world
        if camera_extrinsic is not None:
            # camera_extrinsic IS the pose of the camera in the world frame
            camera_pose_in_world_matrix = camera_extrinsic
        else:
            # If no extrinsic is given, camera system is assumed to be the same as the world system
            camera_pose_in_world_matrix = np.identity(4)

        # Create camera coordinate frame for visualization
        camera_frame_visualization = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.09, origin=[0, 0, 0]) # Slightly smaller
        # You can assign a color, e.g.:
        # camera_frame_visualization.paint_uniform_color([0.8, 0.2, 0.2]) # Reddish for camera
        camera_frame_visualization.transform(camera_pose_in_world_matrix)

        # Also include the OBB and world coordinate frame in the visualization
        geometries_to_render = [
            display_pcd,        # The (potentially transformed) point cloud
            obb,                # OBB of pcd_world
            rim_pcd_world,      # Isolated rim points (already in world coords, colored red)
            start_sphere,
            end_sphere,
            grip_sphere,
            target_edge_line,
            gripper_frame,      # Gripper pose visualization
            world_frame_visualization,    # World coordinate system origin
            camera_frame_visualization    # Camera coordinate system pose in world
        ]
        # --- End Prepare geometries ---

        # --- Visualization ---
        if visualize:
            print("Visualizing results in interactive window...")
            o3d.visualization.draw_geometries(
                geometries_to_render,
                window_name="Gripping Pose Calculation (World Aligned)"
            )
            # Prepare output directory
            if output_path:
                os.makedirs(output_path, exist_ok=True)
                base_dir = output_path
            else:
                base_dir = "."

            o3d.io.write_triangle_mesh(os.path.join(base_dir, "gripper_frame.ply"), gripper_frame)
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "start_sphere.ply"), start_sphere)
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "end_sphere.ply"), end_sphere)
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "grip_sphere.ply"), grip_sphere)
            o3d.io.write_line_set(os.path.join(base_dir, "target_edge_line.ply"), target_edge_line)

            result_filename = "display_pcd.ply"
            rim_filename = "rim_pcd_world.ply"
            o3d.io.write_point_cloud(os.path.join(base_dir, result_filename), display_pcd, write_ascii=True)
            o3d.io.write_point_cloud(os.path.join(base_dir, rim_filename), rim_pcd_world, write_ascii=True)
            print(f"Result saved to: {os.path.join(base_dir, result_filename)}")
        # --- End Visualization ---

        return gripping_point, rotation_matrix, target_edge_points

    except Exception as e:
        print(f"An error occurred during processing: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None


def find_grasp_points_min_area_rect(rim_points_2d):
    """
    Find grasp points using minimum area rectangle.

    Args:
        rim_points_2d (np.ndarray): 2D points representing the rim projection

    Returns:
        tuple: (point1_2d, point2_2d, box) - Two grasp points and the rectangle vertices
    """
    # points to cv format
    points = rim_points_2d.astype(np.float32)

    # compute minimum area rectangle
    rect = cv2.minAreaRect(points)
    box = cv2.boxPoints(rect)
    box = np.array(box)

    # get rectangle center, width, height, and angle
    center, (width, height), angle = rect

    # compute rectangle edges
    edges = []
    for i in range(4):
        edges.append((box[i], box[(i+1)%4]))

    # sort edges by length
    edge_lengths = [np.linalg.norm(edge[0] - edge[1]) for edge in edges]
    sorted_edges_idx = np.argsort(edge_lengths)

    # Take the two shortest edges
    short_edges = [edges[sorted_edges_idx[0]], edges[sorted_edges_idx[1]]]

    # --- Begin of Strategy 1: compute midpoints(1/2) of short edges ---
    # point1_2d = (short_edges[0][0] + short_edges[0][1]) / 2.0
    # point2_2d = (short_edges[1][0] + short_edges[1][1]) / 2.0
    # --- End of Strategy 1 ---

    # --- Begin of Strategy 2: compute 3/4 of short edges ---
    point1_2d = (short_edges[0][0] + short_edges[0][1]) / 2.0
    point2_2d = (short_edges[1][0] + short_edges[1][1]) / 2.0

    edge1_start, edge1_end = short_edges[0]
    edge2_start, edge2_end = short_edges[1]
    
    # Calculate 3/4 point on first short edge
    edge1_vector = edge1_end - edge1_start
    point1_2d_3_4 = edge1_start + 0.75 * edge1_vector
    
    # Calculate 3/4 point on second short edge  
    edge2_vector = edge2_end - edge2_start
    point2_2d_3_4 = edge2_start + 0.75 * edge2_vector

    # Direction-constrained nearest neighbor search
    # This ensures we find points that are both close and in the correct direction
    # --- Begin of Direction-constrained nearest neighbor search ---
    def find_direction_constrained_nearest_point(target_point, connection_vector, rim_points, angle_threshold_deg=10):
        """
        Find nearest point in rim_points that is within angle_threshold of the connection line direction
        
        Args:
            target_point: The calculated 3/4 position
            connection_vector: Direction vector of the connection line between the two 3/4 points
            rim_points: All available rim points
            angle_threshold_deg: Maximum angle deviation in degrees. 0 ~ 90
        
        Returns:
            The nearest point that satisfies direction constraint
        """
        # Normalize connection vector
        connection_direction = connection_vector / np.linalg.norm(connection_vector)
        
        # Calculate vectors from target point to all rim points
        point_vectors = rim_points - target_point
        
        # Filter points that are in the general direction of the connection line
        valid_indices = []
        angle_threshold_rad = np.deg2rad(angle_threshold_deg)
        
        for i, point_vec in enumerate(point_vectors):
            if np.linalg.norm(point_vec) > 0:  # Avoid zero vectors
                point_direction = point_vec / np.linalg.norm(point_vec)
                # Calculate angle between connection direction and point direction
                cos_angle = np.clip(np.dot(connection_direction, point_direction), -1.0, 1.0)
                angle = np.arccos(cos_angle)
                
                # Check if angle is within threshold (also consider opposite direction)
                if angle <= angle_threshold_rad or (np.pi - angle) <= angle_threshold_rad:
                    valid_indices.append(i)
        
        # If no points found within angle constraint, fallback to simple nearest neighbor
        if len(valid_indices) == 0:
            print(f"Warning: No points found within {angle_threshold_deg}° constraint, using simple nearest neighbor")
            distances = np.linalg.norm(rim_points - target_point, axis=1)
            nearest_idx = np.argmin(distances)
            return rim_points[nearest_idx], nearest_idx, distances[nearest_idx]
        
        # Among valid points, find the nearest one to target_point
        valid_points = rim_points[valid_indices]
        distances_to_target = np.linalg.norm(valid_points - target_point, axis=1)
        local_nearest_idx = np.argmin(distances_to_target)
        global_nearest_idx = valid_indices[local_nearest_idx]
                
        return rim_points[global_nearest_idx], global_nearest_idx, distances_to_target[local_nearest_idx]
    # --- End of Direction-constrained nearest neighbor search ---
    # Calculate the connection line direction between the two 3/4 points
    connection_vector = point2_2d_3_4 - point1_2d_3_4
    
    # Find direction-constrained nearest points for both 3/4 positions
    # Use the same connection line direction for both points
    actual_point1_2d, idx1, distance_to_target1 = find_direction_constrained_nearest_point(
        point1_2d_3_4, connection_vector, rim_points_2d
    )
    actual_point2_2d, idx2, distance_to_target2 = find_direction_constrained_nearest_point(
        point2_2d_3_4, connection_vector, rim_points_2d
    )
    
    # Update the grasp points to use actual rim points
    # point1_2d = actual_point1_2d
    # point2_2d = actual_point2_2d
    
    print(f"Found direction-constrained nearest rim points:")
    print(f"  Point 1: calculated {point1_2d_3_4} -> actual {actual_point1_2d} (index: {idx1})")
    print(f"  Point 2: calculated {point2_2d_3_4} -> actual {actual_point2_2d} (index: {idx2})")
    print(f"  Distance to target: {distance_to_target1:.4f}, {distance_to_target2:.4f}")
    # --- End of Strategy 2 ---

    # inset slightly to avoid edges
    # inset_factor = 0.005

    inset_factor_1 = distance_to_target1
    inset_factor_2 = distance_to_target2
    vec = point2_2d - point1_2d
    normalized_vec = vec / np.linalg.norm(vec)
    point1_2d = point1_2d + normalized_vec * inset_factor_1
    point2_2d = point2_2d - normalized_vec * inset_factor_2

    # print information
    print(f"Minimum area rectangle - width: {min(width, height):.4f}, height: {max(width, height):.4f}")
    # print(f"Selected centers of short edges as target points (Strategy 1)")
    print(f"Selected 3/4 points on short edges as target points (Strategy 2)")

    visualize_points_rectangle(box, points, point1_2d, point2_2d)

    return point1_2d, point2_2d, box


def visualize_points_rectangle(box, points, point1_2d, point2_2d):
    """
    Visualization of minimum area rectangle, grasp points, and all 2D input points

    Args:
        box (np.ndarray): 4x2, rectangle four vertices
        points (np.ndarray): Nx2, all 2D points
        point1_2d, point2_2d (np.ndarray): two grasp points
    """
    box_closed = np.vstack([box, box[0]])  # close the rectangle
    plt.figure(figsize=(6,6))
    # plot all 2D points
    plt.scatter(points[:,0], points[:,1], c='g', s=20, marker='.', label='All 2D Points', alpha=0.7)
    # plot rectangle
    plt.plot(box_closed[:,0], box_closed[:,1], 'b-', label='Rectangle')
    # plot grasp points
    plt.scatter([point1_2d[0], point2_2d[0]], [point1_2d[1], point2_2d[1]], c='r', s=80, marker='o', label='Grasp Points')
    plt.legend()
    plt.axis('equal')
    plt.title('Grasp Points and Minimum Area Rectangle')
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.grid(True)
    plt.show()


# --- Main Execution ---
if __name__ == "__main__":
    point_cloud_file = "data/align_color_out.ply"

    # --- Parameters ---
    # This factor is now relative to the height range in world coordinates
    rim_threshold = 0.18 # Might need adjustment (e.g., 10% of height range)

    # Example camera extrinsic matrix (4x4 transformation from camera to world coordinates)
    # This should be replaced with the actual calibrated matrix
    # Identity matrix means no transformation (camera frame = world frame)
    T_camera_to_endeffector = np.array([
        [0.107116, 0.9941, 0.0170677, -0.138795],
        [-0.994209, 0.106946, 0.0105474, 0.0239795],
        [0.00865988, -0.0180987, 0.999799, 0.0404092],
        [0, 0, 0, 1]
    ])
    T_endeffector_to_world = np.array([])

    T_world = T_endeffector_to_world @ T_camera_to_endeffector
    # --- Run Calculation ---
    grip_pos, grip_rot, edge_pts = calculate_gripping_pose_base(
        point_cloud_file,
        rim_height_threshold_factor=rim_threshold,
        visualize=True, # Set to True to see the window
        output_path="out_grasp/out_base_18-1",
        camera_extrinsic=T_world)

    if grip_pos is not None and grip_rot is not None:
        print("\n--- Results ---")
        print(f"Gripping Point(Mid) Position (World): {grip_pos}")
        print(f"Gripping Point(Edge) Position (World): {edge_pts}")
        print("Gripping Orientation (Rotation Matrix - World):")
        print(grip_rot)
    else:
        print("\nFailed to calculate gripping pose.")