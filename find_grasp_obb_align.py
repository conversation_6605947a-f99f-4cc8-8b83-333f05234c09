# Import necessary libraries
import open3d as o3d
import numpy as np
from scipy.spatial import ConvexHull
import copy # To copy geometry for visualization
import os   # To handle file paths
import cv2
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon

def calculate_gripping_pose_obb(ply_file_path,
                                rim_height_threshold_factor=0.05, # Adjusted default
                                visualize=True,
                                output_path=None):
    """
    Calculates the gripping pose (position and orientation) for the center
    of the longest top edge of a basket from a point cloud file, using
    Oriented Bounding Box (OBB) alignment for robustness to orientation.

    Args:
        ply_file_path (str or o3d.geometry.PointCloud): Path to the input PLY point cloud file or a PointCloud object.
        rim_height_threshold_factor (float): Percentage of the OBB's height extent
                                             used to define the rim thickness in the
                                             aligned coordinate system.
                                             Adjust based on point cloud density/noise.
        visualize (bool): If True, displays the point cloud, OBB, rim, edge,
                          and gripping pose using Open3D's interactive window.
        output_path (str, optional): Directory to save visualization outputs. Defaults to None.

    Returns:
        tuple: A tuple containing:
            - gripping_point (np.ndarray): The 3D coordinates (x, y, z) of the
                                           calculated gripping point in the original world frame.
                                           Returns None if processing fails.
            - rotation_matrix (np.ndarray): A 3x3 rotation matrix representing the
                                            gripper's orientation at the gripping point
                                            in the original world frame.
                                            Returns None if processing fails.
            - target_edge_points (np.ndarray): The 3D coordinates of the two endpoints
                                               of the identified target edge in the original world frame.
                                               Returns None if processing fails.
    """
    print(f"Loading point cloud from: {ply_file_path}")
    try:
        # 1. Load Point Cloud
        if isinstance(ply_file_path, o3d.geometry.PointCloud):
            pcd = ply_file_path
            print("Using provided PointCloud object; skipping file read.")
        else:
            pcd = o3d.io.read_point_cloud(ply_file_path)
            if not pcd.has_points():
                print("Error: Point cloud is empty or could not be read.")
                return None, None, None

        print(f"Loaded point cloud with {len(pcd.points)} points.")
        pcd_orig_for_vis = copy.deepcopy(pcd) # Keep original for final visualization

        # --- Optional Preprocessing ---
        # Consider applying preprocessing *before* OBB calculation if needed
        pcd = pcd.voxel_down_sample(voxel_size=0.005)
        pcd, ind = pcd.remove_statistical_outlier(nb_neighbors=50, std_ratio=1.5)
        # --- End Optional Preprocessing ---

    except Exception as e:
        print(f"Error loading or preprocessing point cloud: {e}")
        return None, None, None

    try:
        # 2. Calculate Oriented Bounding Box (OBB)
        print("Calculating Oriented Bounding Box (OBB)...")
        obb = pcd.get_oriented_bounding_box()
        # --- vis obb line ---
        # line_set = o3d.geometry.LineSet.create_from_oriented_bounding_box(obb)
        # o3d.io.write_line_set("obb_lines.ply", line_set)
        # --- END obb line ---

        obb.color = [0, 1, 0] # Green color for OBB visualization
        obb_center = obb.center
        obb_rotation = obb.R # Rotation matrix from world to OBB axes
        obb_extent = obb.extent # Dimensions along OBB axes (x, y, z)
        print(f"OBB Center: {obb_center}")
        print(f"OBB Extent (dimensions): {obb_extent}")
        # print(f"OBB Rotation:\n{obb_rotation}")

        # 2.1 Align OBB with rectangular box edges
        print("Checking and correcting OBB alignment with rectangular edges...")
        obb_rotation = align_obb_with_rectangle(pcd, obb_center, obb_rotation, obb_extent)

        # --- Save original world axes at OBB center for comparison ---
        # orig_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1, origin=obb_center)
        # o3d.io.write_triangle_mesh("orig_axes.ply", orig_frame)
        # print("Saved original world axes to: orig_axes.ply")
        # --- end of world axes ---

        # Save OBB axes as coordinate frame
        obb_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1, origin=obb_center)
        obb_frame.rotate(obb_rotation, center=obb_center)
        # o3d.io.write_triangle_mesh("obb_axes.ply", obb_frame)
        # print("Saved OBB axes to: obb_axes.ply")

        # 3. Align Point Cloud to OBB Axes
        print("Aligning point cloud to OBB axes...")
        pcd_aligned = copy.deepcopy(pcd)
        # Translate center to origin, rotate by inverse rotation, effectively aligning OBB with world axes
        pcd_aligned.rotate(obb_rotation.T, center=obb_center) # Rotate around OBB center first
        pcd_aligned.translate(-obb_center) # Then translate center to origin

        # --- Verification ---
        # aligned_obb = pcd_aligned.get_axis_aligned_bounding_box() # Should now be axis-aligned
        # print(f"Aligned AABB Center: {aligned_obb.get_center()}") # Should be near [0,0,0]
        # print(f"Aligned AABB Extent: {aligned_obb.get_extent()}") # Should match obb_extent (possibly reordered)
        # --- End Verification ---

        # 4. Isolate Upper Rim Points (in Aligned Coordinates)
        aligned_points = np.asarray(pcd_aligned.points)

        # Determine which OBB axis corresponds to 'height'. Often the smallest extent for flat boxes,
        # but could be largest. Let's assume Z (index 2) in the ALIGNED frame corresponds to height.
        # IMPORTANT: Verify this assumption based on your basket's typical shape and OBB extent.
        height_axis_index = 2
        print(f"Assuming OBB axis {height_axis_index} (extent: {obb_extent[height_axis_index]:.3f}) corresponds to height.")

        min_h_aligned = np.min(aligned_points[:, height_axis_index])
        max_h_aligned = np.max(aligned_points[:, height_axis_index])

        # Use OBB extent along the height axis for relative thresholding
        rim_threshold_value = max_h_aligned - obb_extent[height_axis_index] * rim_height_threshold_factor

        rim_indices_aligned = np.where(aligned_points[:, height_axis_index] >= rim_threshold_value)[0]

        if len(rim_indices_aligned) < 3:
             print(f"Error: Not enough points found in the upper rim in aligned coordinates (found {len(rim_indices_aligned)}). Adjust rim_height_threshold_factor or check point cloud/OBB.")
             return None, None, None

        rim_points_3d_aligned = aligned_points[rim_indices_aligned]
        rim_pcd_aligned = o3d.geometry.PointCloud()
        rim_pcd_aligned.points = o3d.utility.Vector3dVector(rim_points_3d_aligned)
        rim_pcd_aligned.paint_uniform_color([1, 0, 0]) # For debugging aligned view
        print(f"Isolated {len(rim_points_3d_aligned)} rim points in aligned coordinates.")

        # 5. Identify Target Edge
        # Project rim points onto the plane orthogonal to the height axis
        # If height is Z (idx 2), project onto XY (idx 0, 1)
        projection_axes = [i for i in range(3) if i != height_axis_index]
        rim_points_2d_aligned = rim_points_3d_aligned[:, projection_axes]
        
        # # --- BEGIN ALIGNMENT (in Aligned Coordinates) ---
        # # NEW METHOD: Use bounding rectangle instead of convex hull. (Axis-Aligned Bounding Box)
        # min_vals = np.min(rim_points_2d_aligned, axis=0)
        # max_vals = np.max(rim_points_2d_aligned, axis=0)
        # width_bb = max_vals[0] - min_vals[0]
        # height_bb = max_vals[1] - min_vals[1]
        # print(f"Bounding rectangle width: {width_bb:.4f}, height: {height_bb:.4f} (aligned 2D)")

        # # Centers of the two shorter edges
        # if width_bb <= height_bb:  # Shorter edges are the horizontal ones (along X)
        #     mid_x = (min_vals[0] + max_vals[0]) / 2.0
        #     point1_2d = np.array([mid_x, min_vals[1] + 0.005])
        #     point2_2d = np.array([mid_x, max_vals[1] - 0.005])
        #     print("Selected centers of horizontal edges as target points (aligned 2D).")
        # else:  # Shorter edges are the vertical ones (along Y)
        #     mid_y = (min_vals[1] + max_vals[1]) / 2.0
        #     point1_2d = np.array([min_vals[0] + 0.005, mid_y])
        #     point2_2d = np.array([max_vals[0] - 0.005, mid_y])
        #     print("Selected centers of vertical edges as target points (aligned 2D).")
        # # --- END ALIGNMENT (in Aligned Coordinates) ---

        # --- BEGIN ALIGNMENT (Min Bounding Box) ---
        point1_2d, point2_2d, box = find_grasp_points_min_area_rect(rim_points_2d_aligned)
        # --- END ALIGNMENT (Min Bounding Box) ---

        # Reconstruct 3D aligned coordinates by adding average height
        # Correct height assignment: use local z maximum for each endpoint
        tol = 1e-3
        mask1 = np.isclose(rim_points_2d_aligned[:, 0], point1_2d[0], atol=tol) & np.isclose(rim_points_2d_aligned[:, 1], point1_2d[1], atol=tol)
        heights1 = rim_points_3d_aligned[mask1, height_axis_index]
        if heights1.size > 0:
            h1 = heights1.max()
        else:
            idx_nn1 = np.argmin(np.linalg.norm(rim_points_2d_aligned - point1_2d, axis=1))
            h1 = rim_points_3d_aligned[idx_nn1, height_axis_index]

        mask2 = np.isclose(rim_points_2d_aligned[:, 0], point2_2d[0], atol=tol) & np.isclose(rim_points_2d_aligned[:, 1], point2_2d[1], atol=tol)
        heights2 = rim_points_3d_aligned[mask2, height_axis_index]
        if heights2.size > 0:
            h2 = heights2.max()
        else:
            idx_nn2 = np.argmin(np.linalg.norm(rim_points_2d_aligned - point2_2d, axis=1))
            h2 = rim_points_3d_aligned[idx_nn2, height_axis_index]

        target_edge_p1_aligned = np.zeros(3)
        target_edge_p2_aligned = np.zeros(3)
        # Set projections
        target_edge_p1_aligned[projection_axes[0]] = point1_2d[0]
        target_edge_p1_aligned[projection_axes[1]] = point1_2d[1]
        target_edge_p2_aligned[projection_axes[0]] = point2_2d[0]
        target_edge_p2_aligned[projection_axes[1]] = point2_2d[1]
        # Set heights
        h = max(h1, h2)
        target_edge_p1_aligned[height_axis_index] = h
        target_edge_p2_aligned[height_axis_index] = h
        # --- END ALIGNMENT ---

        # For consistency with previous logic
        longest_edge_len_aligned = np.linalg.norm(target_edge_p1_aligned - target_edge_p2_aligned)

        print(f"Identified longest edge (aligned) with length: {longest_edge_len_aligned:.4f}")

        # 6. Calculate Gripping Point (in Aligned Coordinates)
        gripping_point_aligned = (target_edge_p1_aligned + target_edge_p2_aligned) / 2.0
        print(f"Calculated Gripping Point (Aligned): {gripping_point_aligned}")

        # 7. Determine Orientation (in Aligned Coordinates)
        edge_vector_aligned = target_edge_p2_aligned - target_edge_p1_aligned
        x_axis_aligned = edge_vector_aligned / np.linalg.norm(edge_vector_aligned)

        # Approach vector (assuming approach is along negative 'height' axis in aligned frame)
        z_axis_desired_aligned = np.zeros(3)
        z_axis_desired_aligned[height_axis_index] = -1.0 # Approach along negative assumed height axis

        # Calculate orthogonal axes in the aligned frame
        if np.abs(np.dot(x_axis_aligned, z_axis_desired_aligned)) > 0.99:
            print("Warning: Edge is nearly parallel to assumed height axis. Adjusting orientation calculation.")
            # Find a different reference vector orthogonal to x_axis_aligned
            temp_ref_aligned = np.zeros(3)
            # Try world X (index 0) unless x_axis is aligned with it
            temp_ref_axis_idx = 0
            if np.abs(x_axis_aligned[temp_ref_axis_idx]) > 0.99:
                temp_ref_axis_idx = 1 # Try world Y (index 1)
            temp_ref_aligned[temp_ref_axis_idx] = 1.0

            y_axis_aligned = np.cross(z_axis_desired_aligned, x_axis_aligned) # This cross product might be zero if aligned
            if np.linalg.norm(y_axis_aligned) < 1e-6: # Check if cross product is near zero
                 y_axis_aligned = np.cross(x_axis_aligned, temp_ref_aligned) # Use alternative reference

            y_axis_aligned /= np.linalg.norm(y_axis_aligned)
            z_axis_aligned = np.cross(x_axis_aligned, y_axis_aligned)
            z_axis_aligned /= np.linalg.norm(z_axis_aligned)
        else:
            y_axis_aligned = np.cross(z_axis_desired_aligned, x_axis_aligned)
            y_axis_aligned /= np.linalg.norm(y_axis_aligned)
            z_axis_aligned = np.cross(x_axis_aligned, y_axis_aligned)
            z_axis_aligned /= np.linalg.norm(z_axis_aligned)

        rotation_matrix_aligned = np.stack([x_axis_aligned, y_axis_aligned, z_axis_aligned], axis=1)
        print("Calculated Rotation Matrix (Aligned):")
        print(rotation_matrix_aligned)

        # 8. Transform Results Back to Original World Coordinates
        print("Transforming results back to original world coordinates...")

        # Rotation: Apply OBB rotation back
        # WorldRotation = OBB_Rotation * AlignedRotation
        rotation_matrix = np.dot(obb_rotation, rotation_matrix_aligned)

        # Points: Rotate first (using OBB rotation), then translate back
        # Create temporary PointCloud objects for easy transformation
        grip_pcd_aligned = o3d.geometry.PointCloud()
        grip_pcd_aligned.points = o3d.utility.Vector3dVector([gripping_point_aligned])
        grip_pcd_world = copy.deepcopy(grip_pcd_aligned).rotate(obb_rotation, center=[0,0,0]).translate(obb_center)
        gripping_point = np.asarray(grip_pcd_world.points)[0]

        edge_p1_pcd_aligned = o3d.geometry.PointCloud()
        edge_p1_pcd_aligned.points = o3d.utility.Vector3dVector([target_edge_p1_aligned])
        edge_p1_world = copy.deepcopy(edge_p1_pcd_aligned).rotate(obb_rotation, center=[0,0,0]).translate(obb_center)
        target_edge_p1 = np.asarray(edge_p1_world.points)[0]

        edge_p2_pcd_aligned = o3d.geometry.PointCloud()
        edge_p2_pcd_aligned.points = o3d.utility.Vector3dVector([target_edge_p2_aligned])
        edge_p2_world = copy.deepcopy(edge_p2_pcd_aligned).rotate(obb_rotation, center=[0,0,0]).translate(obb_center)
        target_edge_p2 = np.asarray(edge_p2_world.points)[0]

        target_edge_points = np.array([target_edge_p1, target_edge_p2])

        print(f"Final Gripping Point (World): {gripping_point}")
        print("Final Rotation Matrix (World):")
        print(rotation_matrix)
        print(f"Final Target Edge Points (World): {target_edge_points.flatten()}")


        # --- Prepare geometries for visualization/saving in WORLD coordinates ---
        gripper_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.05, origin=[0, 0, 0])
        # Apply the FINAL world rotation and translation
        gripper_frame.rotate(rotation_matrix, center=[0, 0, 0])
        gripper_frame.translate(gripping_point)

        # Save gripper coordinate frame at start endpoint
        gripper_frame_p1 = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.05, origin=[0,0,0])
        gripper_frame_p1.rotate(rotation_matrix, center=[0,0,0])
        gripper_frame_p1.translate(target_edge_p1)

        # Save gripper coordinate frame at end endpoint
        gripper_frame_p2 = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.05, origin=[0,0,0])
        gripper_frame_p2.rotate(rotation_matrix, center=[0,0,0])
        gripper_frame_p2.translate(target_edge_p2)

        start_sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.005).paint_uniform_color([0, 1, 0]).translate(target_edge_p1)
        end_sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.005).paint_uniform_color([0, 0, 1]).translate(target_edge_p2)
        grip_sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.007).paint_uniform_color([1, 1, 0]).translate(gripping_point)

        target_edge_line = o3d.geometry.LineSet(
            points=o3d.utility.Vector3dVector(target_edge_points),
            lines=o3d.utility.Vector2iVector([[0, 1]])
        )
        target_edge_line.colors = o3d.utility.Vector3dVector([[1, 1, 0]]) # Yellow line

        # Use original point cloud for display
        display_pcd = pcd.paint_uniform_color([0.5, 0.5, 0.5])
        display_pcd_aligned = pcd_aligned.paint_uniform_color([0, 1, 0])

        # Also include the OBB in the visualization
        geometries_to_render = [display_pcd, obb, start_sphere, end_sphere, grip_sphere, target_edge_line, gripper_frame]
        # --- End Prepare geometries ---

        # --- Visualization ---
        if visualize:
            print("Visualizing results in interactive window...")
            # o3d.visualization.draw_geometries(
            #     geometries_to_render,
            #     window_name="Gripping Pose Calculation (OBB Aligned)"
            # )
            # Prepare output directory
            if output_path:
                os.makedirs(output_path, exist_ok=True)
                base_dir = output_path
            else:
                base_dir = "."

            o3d.io.write_triangle_mesh(os.path.join(base_dir, "gripper_frame.ply"), gripper_frame)
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "start_sphere.ply"), start_sphere)
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "end_sphere.ply"), end_sphere)
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "grip_sphere.ply"), grip_sphere)
            o3d.io.write_line_set(os.path.join(base_dir, "target_edge_line.ply"), target_edge_line)
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "gripper_frame_p1.ply"), gripper_frame_p1)
            print("Saved gripper frame at start endpoint to:", os.path.join(base_dir, "gripper_frame_p1.ply"))
            o3d.io.write_triangle_mesh(os.path.join(base_dir, "gripper_frame_p2.ply"), gripper_frame_p2)
            print("Saved gripper frame at end endpoint to:", os.path.join(base_dir, "gripper_frame_p2.ply"))

            result_filename = "display_pcd.ply"
            display_align_filename = "display_pcd_aligned.ply"
            rim_filename = "rim_pcd_aligned.ply"
            o3d.io.write_point_cloud(os.path.join(base_dir, result_filename), display_pcd, write_ascii=True)
            o3d.io.write_point_cloud(os.path.join(base_dir, display_align_filename), display_pcd_aligned, write_ascii=True)
            o3d.io.write_point_cloud(os.path.join(base_dir, rim_filename), rim_pcd_aligned, write_ascii=True)
            print(f"Result saved to: {os.path.join(base_dir, result_filename)}")
        # --- End Visualization ---


        return gripping_point, rotation_matrix, target_edge_points

    except Exception as e:
        print(f"An error occurred during processing: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None


def find_grasp_points_min_area_rect(rim_points_2d_aligned):
    # points to cv format
    points = rim_points_2d_aligned.astype(np.float32)
    
    # compute minimum area rectangle
    rect = cv2.minAreaRect(points)
    box = cv2.boxPoints(rect)
    box = np.array(box)
    
    # get rectangle center, width, height, and angle
    center, (width, height), angle = rect
    
    # determine short edge direction
    is_width_shorter = width <= height
    
    # compute rectangle edges
    edges = []
    for i in range(4):
        edges.append((box[i], box[(i+1)%4]))
    
    # sort edges by length
    edge_lengths = [np.linalg.norm(edge[0] - edge[1]) for edge in edges]
    sorted_edges_idx = np.argsort(edge_lengths)
    
    # get short edges
    # The two shortest edges are always the first two in the sorted list by length.
    # Their indices in the original `edges` list are sorted_edges_idx[0] and sorted_edges_idx[1].
    short_edge_1 = edges[sorted_edges_idx[0]]
    short_edge_2 = edges[sorted_edges_idx[1]]
    short_edges = [short_edge_1, short_edge_2]
    
    # compute midpoints of short edges
    point1_2d = (short_edges[0][0] + short_edges[0][1]) / 2.0
    point2_2d = (short_edges[1][0] + short_edges[1][1]) / 2.0
    
    # inset slightly to avoid edges
    inset_factor = 0.005
    vec = point2_2d - point1_2d
    # Only apply inset if the midpoints are distinct and inset is meaningful
    if np.linalg.norm(vec) > 1e-6: # Use a small epsilon to avoid division by zero or issues with tiny vectors
        normalized_vec = vec / np.linalg.norm(vec)
        point1_2d = point1_2d + normalized_vec * inset_factor
        point2_2d = point2_2d - normalized_vec * inset_factor
    
    # print information
    print(f"Minimum area rectangle - width: {min(width, height):.4f}, height: {max(width, height):.4f}")
    print(f"Selected centers of the short edges as target points")

    visualize_grasp_points(rim_points_2d_aligned, box, point1_2d, point2_2d)
    
    return point1_2d, point2_2d, box


def visualize_grasp_points(points, box, grasp_point1, grasp_point2):
    """Visualize input points, minimum area bounding rectangle, and grasp points."""
    plt.figure(figsize=(8, 8))
    
    # Plot input points
    plt.scatter(points[:, 0], points[:, 1], c='blue', s=30, label='Input Points')
    
    # Plot minimum area bounding rectangle
    rect_patch = Polygon(box, closed=True, edgecolor='red', linewidth=2, fill=False)
    plt.gca().add_patch(rect_patch)
   
    # Plot grasp points
    plt.scatter(grasp_point1[0], grasp_point1[1], c='green', s=100, marker='o', label='Grasp Point 1')
    plt.scatter(grasp_point2[0], grasp_point2[1], c='green', s=100, marker='o', label='Grasp Point 2')
    
    # Plot line connecting grasp point 1 to grasp point 2
    plt.plot([grasp_point1[0], grasp_point2[0]], [grasp_point1[1], grasp_point2[1]], 'g--', linewidth=1.5)
    
    plt.grid(True)
    plt.axis('equal')  # Ensure x and y axes have the same scale to avoid rectangle distortion
    plt.title('Grasp Points and Minimum Area Rectangle')
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.legend()
    plt.tight_layout()
    plt.show()


def align_obb_with_rectangle(pcd, obb_center, obb_rotation, obb_extent):
    """
    Aligns the OBB coordinate system with the actual rectangular edges of the box.

    Args:
        pcd (o3d.geometry.PointCloud): The input point cloud
        obb_center (np.ndarray): The center of the OBB
        obb_rotation (np.ndarray): The rotation matrix of the OBB
        obb_extent (np.ndarray): The extent (dimensions) of the OBB

    Returns:
        np.ndarray: The corrected rotation matrix aligned with rectangular edges
    """
    # Create a copy of the point cloud for processing
    pcd_copy = copy.deepcopy(pcd)

    # Transform point cloud to OBB coordinate system
    pcd_copy.rotate(obb_rotation.T, center=obb_center)
    pcd_copy.translate(-obb_center)
    points = np.asarray(pcd_copy.points)

    # Find the principal directions using PCA on projected points
    # We'll analyze points projected onto the XY plane (assuming Z is height)
    xy_points = points[:, 0:2]  # Extract X and Y coordinates

    # Compute covariance matrix of the projected points
    mean = np.mean(xy_points, axis=0)
    centered_points = xy_points - mean
    cov = np.cov(centered_points, rowvar=False)

    # Get eigenvectors and eigenvalues
    eigenvalues, eigenvectors = np.linalg.eigh(cov)

    # Sort eigenvectors by eigenvalues in descending order
    idx = eigenvalues.argsort()[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]

    # The eigenvectors now represent the principal directions in the XY plane
    # We want to align these with the X and Y axes

    # Create a rotation matrix for the XY plane
    correction_2d = eigenvectors

    # Create a 3D rotation matrix (identity for Z axis)
    correction_3d = np.eye(3)
    correction_3d[0:2, 0:2] = correction_2d

    # Apply the correction to the original OBB rotation
    # First rotate back to world coordinates, then apply the correction
    corrected_rotation = np.dot(obb_rotation, correction_3d)

    # Check if we need to swap axes to ensure the longest dimension is along X
    if obb_extent[1] > obb_extent[0]:  # If Y extent is greater than X extent
        # Create a 90-degree rotation matrix around Z
        swap_matrix = np.array([
            [0, 1, 0],
            [-1, 0, 0],
            [0, 0, 1]
        ])
        corrected_rotation = np.dot(corrected_rotation, swap_matrix)

    print("OBB rotation corrected to align with rectangular edges")
    return corrected_rotation


def align_obb_by_hull(pcd, obb_center, obb_rotation, obb_extent):
    """
    Aligns the OBB coordinate system using convex hull + minimum bounding rectangle.

    Args:
        pcd (o3d.geometry.PointCloud): The input point cloud
        obb_center (np.ndarray): The center of the OBB
        obb_rotation (np.ndarray): The rotation matrix of the OBB
        obb_extent (np.ndarray): The extent (dimensions) of the OBB

    Returns:
        np.ndarray: The corrected rotation matrix aligned with rectangular edges via hull
    """
    # Copy and transform point cloud to OBB frame
    pcd_copy = copy.deepcopy(pcd)

    # Transform point cloud to OBB coordinate system
    pcd_copy.rotate(obb_rotation.T, center=obb_center)
    pcd_copy.translate(-obb_center)
    points = np.asarray(pcd_copy.points)

    # Project to XY plane
    xy = points[:, :2]
    if len(xy) < 3:
        print("Not enough points for hull alignment")
        return obb_rotation

    # Compute convex hull of projected points
    hull = ConvexHull(xy)
    hull_pts = xy[hull.vertices]

    # Rotating calipers to find minimum-area bounding rectangle
    min_area = np.inf
    best_angle = 0
    best_dims = (0, 0)
    for i in range(len(hull_pts)):
        p1 = hull_pts[i]
        p2 = hull_pts[(i + 1) % len(hull_pts)]
        edge = p2 - p1
        angle = -np.arctan2(edge[1], edge[0])
        # 2D rotation matrix
        R2 = np.array([[np.cos(angle), -np.sin(angle)],
                       [np.sin(angle),  np.cos(angle)]])
        rot_pts = hull_pts.dot(R2.T)
        min_x, max_x = rot_pts[:,0].min(), rot_pts[:,0].max()
        min_y, max_y = rot_pts[:,1].min(), rot_pts[:,1].max()
        area = (max_x - min_x) * (max_y - min_y)
        if area < min_area:
            min_area = area
            best_angle = angle
            best_dims = (max_x - min_x, max_y - min_y)

    # Build 3D correction matrix
    cosb, sinb = np.cos(best_angle), np.sin(best_angle)
    correction_3d = np.eye(3)
    correction_3d[0:2, 0:2] = [[cosb, -sinb], [sinb, cosb]]

    # Apply to original OBB rotation
    corrected_rotation = np.dot(obb_rotation, correction_3d)

    # Ensure longest dimension aligns with X axis
    if best_dims[1] > best_dims[0]:
        swap = np.array([[0, 1, 0],
            [-1, 0, 0],
                         [0, 0, 1]])
        corrected_rotation = np.dot(corrected_rotation, swap)

    print("OBB rotation corrected using hull minimal rectangle")
    return corrected_rotation

# --- Main Execution ---
if __name__ == "__main__":
    point_cloud_file = "data/align_color_out.ply"

    # --- Parameters ---
    # This factor is now relative to the OBB's estimated height dimension
    rim_threshold = 0.18 # Might need adjustment (e.g., 10% of OBB height)

    # --- Run Calculation ---
    grip_pos, grip_rot, edge_pts = calculate_gripping_pose_obb( # Call the new function
        point_cloud_file,
        rim_height_threshold_factor=rim_threshold,
        visualize=True, # Set to True to see the window
        output_path="out_grasp/out_rec_18-1")

    if grip_pos is not None and grip_rot is not None:
        print("\n--- Results ---")
        print(f"Gripping Point(Mid) Position (World): {grip_pos}")
        print(f"Gripping Point(Edge) Position (World): {edge_pts}")
        print("Gripping Orientation (Rotation Matrix - World):")
        print(grip_rot)
    else:
        print("\nFailed to calculate gripping pose.")
