#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Orbbec Camera Interface for RGB and Depth Image Capture

This script provides an interface to capture RGB and depth images using the Orbbec SDK.
Requires the pyorbbecsdk package to be installed.

Usage:
    python orbb.py
"""

import numpy as np
import cv2
import time
import os

try:
    import pyorbbecsdk as orbbec
except ImportError:
    print("Error: pyorbbecsdk package not found.")
    print("Please install it using: pip install pyorbbecsdk")
    exit(1)


class OrbbecCamera:
    """Interface for Orbbec cameras using the pyorbbecsdk."""
    
    def __init__(self):
        """Initialize the Orbbec camera interface."""
        self.context = None
        self.device = None
        self.pipeline = None
        self.config = None
        self.color_stream_profile = None
        self.depth_stream_profile = None
        self.is_running = False
        self.align_processor = None
        
    def connect(self):
        """Connect to the first available Orbbec device."""
        try:
            # Create a context
            self.context = orbbec.Context()
            
            # Get the list of devices
            devices = self.context.query_devices()
            if len(devices) == 0:
                print("No Orbbec device found.")
                return False
            
            # Use the first device
            self.device = devices[0]
            print(f"Connected to: {self.device.get_device_info().get_name()}")
            
            # Create pipeline
            self.pipeline = orbbec.Pipeline(self.context)
            
            # Create and configure the config
            self.config = orbbec.Config()
            
            # Enable color stream
            self.config.enable_stream(orbbec.OBStreamType.OB_STREAM_COLOR, 640, 480, 
                                      orbbec.OBFormat.OB_FORMAT_RGB888)
            
            # Enable depth stream
            self.config.enable_stream(orbbec.OBStreamType.OB_STREAM_DEPTH, 640, 480, 
                                      orbbec.OBFormat.OB_FORMAT_Y16)
            
            # Set the alignment mode (align depth to color)
            self.config.set_align_mode(orbbec.OBAlignMode.ALIGN_D2C_HW_MODE)
            
            # Start the pipeline with the config
            self.pipe_profile = self.pipeline.start(self.config)
            
            # Create frame aligner for software alignment (if hardware alignment isn't sufficient)
            self.align_processor = orbbec.FrameHelper.get_frame_align(self.context, 
                                                                orbbec.OBAlignMode.ALIGN_D2C_SW_MODE)
            
            self.is_running = True
            return True
        
        except Exception as e:
            print(f"Error connecting to Orbbec device: {e}")
            self.disconnect()
            return False
    
    def disconnect(self):
        """Disconnect and clean up resources."""
        try:
            if self.is_running and self.pipeline:
                self.pipeline.stop()
            
            self.pipeline = None
            self.device = None
            self.context = None
            self.is_running = False
            
            print("Disconnected from Orbbec device.")
        except Exception as e:
            print(f"Error during disconnection: {e}")
    
    def capture_frames(self, timeout_ms=5000):
        """Capture RGB and depth frames.
        
        Args:
            timeout_ms (int): Timeout in milliseconds.
            
        Returns:
            tuple: (color_frame, depth_frame) or (None, None) if capture failed.
        """
        if not self.is_running:
            print("Camera is not running. Call connect() first.")
            return None, None
        
        try:
            # Wait for a frameset
            frameset = self.pipeline.wait_for_frames(timeout_ms)
            if not frameset:
                print("No frames received.")
                return None, None
            
            # Get color and depth frames
            color_frame = frameset.get_color_frame()
            depth_frame = frameset.get_depth_frame()
            
            if not color_frame or not depth_frame:
                print("Invalid frames received.")
                return None, None
            
            return color_frame, depth_frame
            
        except Exception as e:
            print(f"Error capturing frames: {e}")
            return None, None
    
    def get_images(self):
        """Get RGB and depth images as numpy arrays.
        
        Returns:
            tuple: (rgb_image, depth_image) as numpy arrays, or (None, None) if capture failed.
        """
        color_frame, depth_frame = self.capture_frames()
        if color_frame is None or depth_frame is None:
            return None, None
        
        try:
            # Convert color frame to numpy array
            color_data = color_frame.get_data()
            width = color_frame.get_width()
            height = color_frame.get_height()
            rgb_image = np.frombuffer(color_data, dtype=np.uint8).reshape(height, width, 3)
            
            # Convert depth frame to numpy array
            depth_data = depth_frame.get_data()
            width = depth_frame.get_width()
            height = depth_frame.get_height()
            depth_image = np.frombuffer(depth_data, dtype=np.uint16).reshape(height, width)
            
            return rgb_image, depth_image
            
        except Exception as e:
            print(f"Error converting frames to images: {e}")
            return None, None
    
    def save_images(self, folder="./images"):
        """Capture and save RGB and depth images to files.
        
        Args:
            folder (str): Folder to save images.
            
        Returns:
            tuple: (rgb_path, depth_path) or (None, None) if failed.
        """
        rgb_image, depth_image = self.get_images()
        if rgb_image is None or depth_image is None:
            return None, None
        
        try:
            # Create folder if it doesn't exist
            os.makedirs(folder, exist_ok=True)
            
            # Generate filenames with timestamp
            timestamp = int(time.time())
            rgb_filename = f"{timestamp}_rgb.png"
            depth_filename = f"{timestamp}_depth.png"
            
            rgb_path = os.path.join(folder, rgb_filename)
            depth_path = os.path.join(folder, depth_filename)
            
            # Save images
            cv2.imwrite(rgb_path, cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR))
            cv2.imwrite(depth_path, depth_image)
            # Normalize depth image for better visualization when saving
            # depth_normalized = cv2.normalize(depth_image, None, 0, 65535, cv2.NORM_MINMAX)
            # cv2.imwrite(depth_path, depth_normalized)
            
            print(f"Images saved to {rgb_path} and {depth_path}")
            return rgb_path, depth_path
            
        except Exception as e:
            print(f"Error saving images: {e}")
            return None, None
    
    def display_images(self):
        """Display RGB and depth images in OpenCV windows.
        
        Press 'q' to quit the display.
        """
        if not self.is_running:
            print("Camera is not running. Call connect() first.")
            return
        
        try:
            print("Press 'q' to quit, 's' to save images")
            while self.is_running:
                rgb_image, depth_image = self.get_images()
                if rgb_image is None or depth_image is None:
                    time.sleep(0.1)
                    continue
                
                # Normalize depth image for better visualization
                depth_colormap = cv2.normalize(depth_image, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
                depth_colormap = cv2.applyColorMap(depth_colormap, cv2.COLORMAP_JET)
                
                # Display images
                cv2.imshow("RGB Image", cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR))
                cv2.imshow("Depth Image", depth_colormap)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    self.save_images()
            
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"Error displaying images: {e}")
            cv2.destroyAllWindows()

    def get_camera_intrinsics(self):
        """获取相机内参。
        
        Returns:
            dict: 包含相机内参的字典。
        """
        try:
            # 获取彩色相机内参
            color_profile = self.pipe_profile.get_stream_profile(orbbec.OBStreamType.OB_STREAM_COLOR)
            color_intrinsics = color_profile.get_intrinsics()
            
            # 获取深度相机内参
            depth_profile = self.pipe_profile.get_stream_profile(orbbec.OBStreamType.OB_STREAM_DEPTH)
            depth_intrinsics = depth_profile.get_intrinsics()
            
            intrinsics = {
                'color': {
                    'fx': color_intrinsics.fx,
                    'fy': color_intrinsics.fy,
                    'cx': color_intrinsics.cx,
                    'cy': color_intrinsics.cy,
                    'width': color_intrinsics.width,
                    'height': color_intrinsics.height
                },
                'depth': {
                    'fx': depth_intrinsics.fx,
                    'fy': depth_intrinsics.fy,
                    'cx': depth_intrinsics.cx,
                    'cy': depth_intrinsics.cy,
                    'width': depth_intrinsics.width,
                    'height': depth_intrinsics.height
                }
            }
            
            return intrinsics
        
        except Exception as e:
            print(f"获取相机内参失败: {e}")
            return None

def main():
    """Main function to demonstrate Orbbec camera usage."""
    camera = OrbbecCamera()
    
    try:
        if camera.connect():
            # Display RGB and depth images
            # intrinsics = self.get_camera_intrinsics()
            camera.display_images()
    finally:
        camera.disconnect()


if __name__ == "__main__":
    main()
