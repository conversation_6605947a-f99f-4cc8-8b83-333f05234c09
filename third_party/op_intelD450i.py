import pyrealsense2 as rs
import numpy as np
import cv2

class IntelCamera:
    def __init__(self,name='D450i'):

        # 初始化深度相机
        self.pipeline = rs.pipeline()
        config = rs.config()
        config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
        
        self.pipeline.start(config)

    def __del__(self):
        self.pipeline.stop()

    def get_image(self,transnumpy=True):
        frames = self.pipeline.wait_for_frames(timeout_ms=10000)
        depth_frame = frames.get_depth_frame()
        color_frame = frames.get_color_frame()

        if transnumpy:

            if not depth_frame or not color_frame:
                return [None,None]

            # 获取深度图像的原始数据
            depth_data = np.asanyarray(depth_frame.get_data())
    
            # 获取RGB图像的原始数据
            color_data = np.asanyarray(color_frame.get_data())

            return [depth_data,color_data]

        return [depth_frame,color_frame]
    
    def visualize(self,savedir=None):
        
        colorizer = rs.colorizer()

        try:
            while True:
                try:
                    [depth_frame,color_frame] = self.get_image(transnumpy=False)

                    if not depth_frame or not color_frame:
                        continue

                    # 获取RGB图像的原始数据
                    color_data = np.asanyarray(color_frame.get_data())
    

                    # 使用伪彩色映射器将深度数据转换为伪彩色图像
                    depth_colormap = np.asanyarray(colorizer.colorize(depth_frame).get_data())
                    # 创建一个窗口，显示彩色图像和伪彩色深度图像
                    combined_image = np.hstack((color_data, depth_colormap))
                    cv2.imshow("Combined Image", combined_image)
 
                    # 按Esc键退出循环
                    key = cv2.waitKey(1)
                    if key == 27:
                        break
                except RuntimeError as e:
                    print(f"等待帧时发生错误: {e}")

        finally:
            cv2.destroyAllWindows()

    def save_image(self,imgdir):
        pass


if __name__=="__main__":
    irs_camera = IntelCamera()

    irs_camera.visualize()

    # [depthdata,colordata] = irs_camera.get_image()

    # print(['depthdata shape:',depthdata.shape])
    # print(['colordata shape:',colordata.shape])

