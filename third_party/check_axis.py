import open3d as o3d

# 加载点云
pcd = o3d.io.read_point_cloud("data/blue_mesh/output_blue.ply")
# # 半径滤波去噪
# pcd, ind = pcd.remove_radius_outlier(nb_points=16, radius=0.01)
# pcd = pcd.select_by_index(ind)

# 创建世界坐标系
coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1, origin=[0,0,0])

# 计算OBB
obb = pcd.get_oriented_bounding_box()
obb.color = [0, 1, 0]  # 可视化为绿色

# 在OBB中心创建OBB坐标系，并应用OBB旋转
obb_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1, origin=obb.center)
obb_frame.rotate(obb.R, center=obb.center)

# 合并两个坐标系
all_frames = coord_frame + obb_frame

# 保存点云
# o3d.io.write_point_cloud("data/blue_mesh/with_axis.ply", pcd)
# 保存合并后的坐标系
o3d.io.write_triangle_mesh("data/blue_mesh/with_axis_frame.ply", obb_frame)


print("Done! 坐标系和OBB已保存。")
