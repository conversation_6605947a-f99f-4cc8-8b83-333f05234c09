import open3d as o3d
import numpy as np
import os
from scipy.spatial.transform import Rotation

def normalize_vector(v):
    """
    Normalize the vector
    """
    norm = np.linalg.norm(v)
    if norm == 0:
        return v
    return v / norm

def calculate_grasp_pose_from_points(cad_pcd, grasp_point1, grasp_point2):
    """
    Calculate the grasp pose based on the CAD model OBB and two user-specified grasp points.
    The grasp pose is first defined in the OBB local coordinate system and then transformed to the world coordinate system.

    Rules (define the grasp axes in the OBB local coordinate system):
    - Transform the grasp points from world coordinates to OBB local coordinates.
    - Use the direction of the line between the two points in the OBB local system as the grasp x-axis.
    - Use the negative direction of the OBB local z-axis (i.e., [0, 0, -1]) as the grasp z-axis.
    - Calculate the y-axis to form a right-handed coordinate system.

    Args:
        cad_pcd (o3d.geometry.PointCloud): CAD model point cloud.
        grasp_point1 (numpy.ndarray): First grasp point coordinate (3,) in world coordinates. CAD
        grasp_point2 (numpy.ndarray): Second grasp point coordinate (3,) in world coordinates. CAD

    Returns:
        numpy.ndarray: Final grasp pose 3x3 rotation matrix.
                       Its column vectors are the basis vectors of the grasp coordinate system in world coordinates.
                       (i.e., R_grasp_to_world)
        numpy.ndarray: Grasp center point (3,) in world (CAD) coordinates.
    """
    # Convert input points to numpy arrays
    grasp_point1_world = np.asarray(grasp_point1).flatten()
    grasp_point2_world = np.asarray(grasp_point2).flatten()
    
    # 1. Compute the OBB of the point cloud
    obb = cad_pcd.get_oriented_bounding_box()
    obb_center_world = obb.center
    obb_rotation_to_world = obb.R  # OBB local coordinate system -> world coordinate system CAD
    obb_rotation_from_world = obb.R.T # world coordinate system CAD -> OBB local coordinate system
    
    # 2. Transform the grasp points from world coordinates to OBB local coordinates
    grasp_point1_obb = obb_rotation_from_world @ (grasp_point1_world - obb_center_world)
    grasp_point2_obb = obb_rotation_from_world @ (grasp_point2_world - obb_center_world)
    
    # 3. Compute the direction of the grasp x-axis in the OBB local coordinate system
    grasp_x_axis_obb = grasp_point2_obb - grasp_point1_obb
    if np.linalg.norm(grasp_x_axis_obb) < 1e-6: # Avoid division by zero if points are coincident
        # Default to OBB's x-axis if grasp points are too close or coincident
        grasp_x_axis_obb = np.array([1.0, 0, 0]) 
    else:
        grasp_x_axis_obb = normalize_vector(grasp_x_axis_obb)
    
    # 4. In the OBB local coordinate system, the grasp z-axis is the negative direction of the local z-axis
    grasp_z_axis_obb = np.array([0, 0, -1.0])
    
    # 5. Compute the grasp y-axis in the OBB local coordinate system (z cross x)
    grasp_y_axis_obb = np.cross(grasp_z_axis_obb, grasp_x_axis_obb)
    if np.linalg.norm(grasp_y_axis_obb) < 1e-6: # Handle case where x_axis is parallel to z_axis
        # If x is aligned with z (e.g., vertical grasp), pick y along OBB's y-axis
        grasp_y_axis_obb = np.array([0, 1.0, 0])
        # Recalculate x to be orthogonal
        grasp_x_axis_obb = np.cross(grasp_y_axis_obb, grasp_z_axis_obb)
        grasp_x_axis_obb = normalize_vector(grasp_x_axis_obb)
        grasp_y_axis_obb = normalize_vector(grasp_y_axis_obb) # Re-normalize y
    else:
        grasp_y_axis_obb = normalize_vector(grasp_y_axis_obb)
        # 6. Recalculate the OBB local x-axis to ensure orthogonality (y cross z)
        grasp_x_axis_obb = np.cross(grasp_y_axis_obb, grasp_z_axis_obb)
        grasp_x_axis_obb = normalize_vector(grasp_x_axis_obb)

    # 7. Build the grasp rotation matrix in the OBB local coordinate system
    # Its column vectors are the basis vectors of the grasp coordinate system in the OBB local coordinate system
    rotation_grasp_to_obb = np.column_stack([grasp_x_axis_obb, grasp_y_axis_obb, grasp_z_axis_obb])
    
    # 8. Transform this rotation matrix back to the world coordinate system
    # R_grasp_to_world = R_obb_to_world @ R_grasp_to_obb
    final_rotation_matrix = obb_rotation_to_world @ rotation_grasp_to_obb
    
    # 9. Calculate the grasp center point (the midpoint of the two points, in world coordinates)
    grasp_center_world = (grasp_point1_world + grasp_point2_world) / 2.0
    
    return final_rotation_matrix, grasp_center_world

def display_grasp_pose(cad_pcd, grasp_rotation, grasp_center, size=0.05):
    """
    Visualize the CAD point cloud and grasp pose

    Args:
        cad_pcd (o3d.geometry.PointCloud): CAD point cloud
        grasp_rotation (numpy.ndarray): 3x3 rotation matrix of the grasp pose
        grasp_center (numpy.ndarray): Grasp center point (3,)
        size (float): Size of the coordinate frame
    """
    # 创建坐标轴表示抓取姿态
    grasp_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=size, origin=[0, 0, 0])
    grasp_frame.rotate(grasp_rotation, center=[0, 0, 0])
    grasp_frame.translate(grasp_center)
    
    # Visualize
    o3d.visualization.draw_geometries([cad_pcd, grasp_frame])

def save_grasp_pose(cad_pcd, grasp_rotation, grasp_center, grasp_point1, grasp_point2, output_path, size=0.05):
    """
    Save the CAD point cloud and grasp pose to a file, and additionally save the grasp-related points as a PLY file.

    Args:
        cad_pcd (o3d.geometry.PointCloud): CAD point cloud
        grasp_rotation (numpy.ndarray): 3x3 rotation matrix of the grasp pose
        grasp_center (numpy.ndarray): Grasp center point (3,)
        grasp_point1 (numpy.ndarray): First grasp contact point (3,)
        grasp_point2 (numpy.ndarray): Second grasp contact point (3,)
        output_path (str): Output path for saving the coordinate frame mesh
        size (float): Size of the coordinate frame
    """
    # 创建坐标轴表示抓取姿态
    grasp_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=size, origin=[0, 0, 0])
    grasp_frame.rotate(grasp_rotation, center=[0, 0, 0])
    grasp_frame.translate(grasp_center)
    
    # Save the coordinate frame representing the grasp pose
    o3d.io.write_triangle_mesh(output_path, grasp_frame)
    print(f"Grasp pose saved to: {output_path}")

    # Construct the file path for saving the grasp points as a PLY file
    base, ext = os.path.splitext(output_path)
    points_ply_output_filename = base + "_grasp_points.ply"
    
    # Create a point cloud containing the grasp center and contact points
    grasp_points_np = np.array([grasp_center, grasp_point1, grasp_point2])
    grasp_points_pcd = o3d.geometry.PointCloud()
    grasp_points_pcd.points = o3d.utility.Vector3dVector(grasp_points_np)
    
    # Add color to the point cloud
    # grasp_center: red, grasp_point1: green, grasp_point2: blue
    colors = np.array([[1, 0, 0], [0, 1, 0], [0, 0, 1]]) 
    grasp_points_pcd.colors = o3d.utility.Vector3dVector(colors)

    # Save the grasp-related point cloud
    o3d.io.write_point_cloud(points_ply_output_filename, grasp_points_pcd)
    print(f"Grasp-related points saved to PLY file: {points_ply_output_filename}")


if __name__ == "__main__":
    # Example usage
    # 1. Load CAD model point cloud
    cad_pcd = o3d.io.read_point_cloud("cad_model.ply")
    
    # 2. Input two grasp points
    grasp_point1 = np.array([0.05, 0.02, 0.01])
    grasp_point2 = np.array([0.10, -0.01, 0.03])

    # 3. Calculate the grasp pose
    grasp_rotation, grasp_center = calculate_grasp_pose_from_points(cad_pcd, grasp_point1, grasp_point2)
    
    print("Grasp rotation matrix:")
    print(grasp_rotation)
    print("Grasp center point:")
    print(grasp_center)

    # 4. Visualize the grasp pose
    # display_grasp_pose(cad_pcd, grasp_rotation, grasp_center)
    
    # 5. Save the grasp pose and points
    save_grasp_pose(cad_pcd, grasp_rotation, grasp_center, grasp_point1, grasp_point2, "grasp_pose.ply")
