from a2d_sdk.robot import CosineCamera as Camera
import time
import cv2
from a2d_sdk.robot import RobotDds as Robot
import time
save = 'img429/'

# Initialize robot
robot = Robot()
print("init finish!")
time.sleep(3)
# Get current state
arm_pos, timestamp = robot.arm_joint_states()
head_pos, timestamp = robot.head_joint_states()

# Initialize camera
cameras_head = ["head"]
cameras_head = Camera(cameras_head)
cameras_depth = ["head_depth"]
cameras_depth = Camera(cameras_depth)
time.sleep(3)
num = 0

while num < 10:
    num += 1
    # Get real-time images
    print("i:", num)
    image, time_stamp_color = cameras_head.get_latest_image("head")
    depth, time_stamp_depth = cameras_depth.get_latest_image("head_depth")
    if image is None and depth is None:
        continue
    print("image:", image.shape)
    print("depth:", depth.shape)
    cv2.imwrite(save+str(num)+".png", image)
    cv2.imwrite(save+str(num)+".tif", depth)

# Close cameras after use
cameras_head.close()
cameras_depth.close()

