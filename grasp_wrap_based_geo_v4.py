import cv2
import sys
import torch
import open3d as o3d
import numpy as np
import PIL.Image
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R_scipy

# Import the modified camera capture function
from utils import capture_images, SAMTRTPredictor, YOLOv8TRTDetector, mask_to_point_cloud
from find_grasp_base_align import calculate_gripping_pose_base


def bbox2points(bbox):
    """Convert bounding box to points for SAM"""
    points = np.array([
        [bbox[0], bbox[1]],  # top-left
        [bbox[2], bbox[3]]   # bottom-right
    ])
    point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
    return points, point_labels

def draw_bbox(bbox, color='g'):
    """Draw bounding box on the plot"""
    x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
    y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
    plt.plot(x, y, color + '-')

def subplot_notick(a, b, c):
    """Create subplot without ticks"""
    ax = plt.subplot(a, b, c)
    ax.set_xticklabels([])
    ax.set_yticklabels([])
    ax.axis('off')

def main():
    # Method 1: Capture images directly from camera (recommended)
    print("Capturing images from Orbbec camera...")
    cv_image, depth, camera_param = capture_images(save_images=False)  # Set to False to skip saving
    
    if cv_image is None or depth is None:
        print("Failed to capture images from camera!")
        sys.exit(1)
    else:
        print("Successfully captured images from camera!")
        # Convert BGR to RGB for PIL
        rgb_array = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        pil_image = PIL.Image.fromarray(rgb_array)
        # Convert depth from uint16 to float32 and scale to meters
        depth = depth.astype(np.float32)
    
    # Scale depth to meters (assuming depth is in millimeters from camera)
    depth = depth / 1000.0   # mm -> m

    # config infer mask
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"device: {device}")
    yolo_engine = 'weights/yolov8s_basket.engine'
    image_encoder = 'weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'weights/mobile_sam_mask_decoder_fp16.engine'

    # Initialize pose estimator
    print("Initialize pose estimator")
    detector = YOLOv8TRTDetector(yolo_engine, device)
    sam_predictor = SAMTRTPredictor(device, image_encoder, mask_decoder)
    
    # yolo infer
    detections = detector.predict(cv_image)
   
    N = len(detections['bboxes'])
    
    if N == 0:
        print("No objects detected with the current settings.")
        sys.exit(0)

    bbox = detections['bboxes'][0]

    # sam infer
    points, point_labels = bbox2points(bbox)
    sam_predictor.set_image(pil_image)
    mask, _, _ = sam_predictor.predict(points, point_labels)
    mask = (mask[0, 0] > 0).detach().cpu().numpy()
    mask = mask.astype(np.uint8) * 255

    if True:
        # pil_mask = PIL.Image.fromarray(mask, 'L')
        # print(f"pil_mask.size: {pil_mask.size}")
        # pil_mask.save("mask.png")

        subplot_notick(2, N, 1)
        plt.imshow(pil_image)
        draw_bbox(bbox)
        plt.title("Detection")

        subplot_notick(2, N, 2)
        plt.imshow(pil_image)
        plt.imshow(mask, alpha=0.5)
        plt.title("Segmentation Mask")
        plt.subplots_adjust(wspace=0.05, hspace=0.2)
        plt.savefig('out.png', bbox_inches="tight")
       
    # estimate pose
    rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)

    # Use camera parameters from camera_param if available, otherwise use hardcoded values
    if camera_param is not None:
        # Extract intrinsic parameters from camera_param
        # Note: You may need to adjust this based on the actual structure of camera_param
        intrinsic = np.array([
            [camera_param.color_intrinsic.fx, 0, camera_param.color_intrinsic.cx],
            [0, camera_param.color_intrinsic.fy, camera_param.color_intrinsic.cy],
            [0, 0, 1]   
        ])
        print(f"Using camera intrinsic parameters: {intrinsic}")
    else:
        # Fallback to hardcoded intrinsic parameters
        intrinsic = np.array([
            [647.4208984375, 0, 643.5106201171875],
            [0, 646.6436157226562, 360.1596374511719],
            [0, 0, 1]   
        ])
        print("Using fallback intrinsic parameters")
    
    # mask to point cloud
    object_pcd = mask_to_point_cloud(rgb, depth, mask, intrinsic)
    
    # --- Parameters ---
    rim_threshold = 0.20 # Might need adjustment

    camera_ext_param_right = np.array([
        [0.107116, 0.9941, 0.0170677, -0.138795],
        [-0.994209, 0.106946, 0.0105474, 0.0239795],
        [0.00865988, -0.0180987, 0.999799, 0.0404092],
        [0, 0, 0, 1]
    ])

    # --- Run Calculation ---
    _, grip_rot, edge_pts = calculate_gripping_pose_base(
        object_pcd, 
        rim_height_threshold_factor=rim_threshold, 
        visualize=True,
        output_path="out_grasp/out_camera_capture",
        camera_extrinsic=camera_ext_param_right)

    if edge_pts is not None and grip_rot is not None:
        print("\n--- Results ---")
        print(f"Gripping Point(Edge) Position (Right - Base): {edge_pts}")
        print("Gripping Orientation (Rotation Matrix - Right - Base):")
        print(grip_rot)
        try:
            r_grip = R_scipy.from_matrix(grip_rot)
            euler_angles_xyz_deg = r_grip.as_euler('xyz', degrees=True)
            print(f"Gripping Orientation (Euler Angles XYZ, degrees - Right - Base): {euler_angles_xyz_deg}")
        except Exception as e:
            print(f"Could not calculate Euler angles: {e}")
    else:
        print("\nFailed to calculate gripping pose.")

    camera_ext_param_left = np.array([
        [0.107116, 0.9241, 0.0170677, 0.17795],
        [-0.994209, 0.156946, 0.0145474, -0.0239795],
        [0.00865988, -0.0180987, 0.999799, -0.0404092],
        [0, 0, 0, 1]
    ])

    # Transform points and rotation from right arm base to left arm base
    if edge_pts is not None and grip_rot is not None:
        # Step 1: Transform from right arm base to camera frame
        # Use inverse of camera_ext_param_right
        camera_ext_param_right_inv = np.linalg.inv(camera_ext_param_right)
        R_c_r = camera_ext_param_right_inv[:3, :3]  # Camera to right arm rotation
        t_c_r = camera_ext_param_right_inv[:3, 3]   # Camera to right arm translation

        # Transform edge points from right arm base to camera frame
        edge_pts_camera = (R_c_r @ edge_pts.T + t_c_r[:, np.newaxis]).T

        # Transform grip rotation from right arm base to camera frame
        grip_rot_camera = R_c_r @ grip_rot

        # Step 2: Transform from camera frame to left arm base frame
        R_l_c = camera_ext_param_left[:3, :3]   # Left arm to camera rotation
        t_l_c = camera_ext_param_left[:3, 3]    # Left arm to camera translation

        # Transform edge points from camera frame to left arm base frame
        edge_pts_left = (R_l_c @ edge_pts_camera.T + t_l_c[:, np.newaxis]).T

        # Transform grip rotation from camera frame to left arm base frame
        grip_rot_left = R_l_c @ grip_rot_camera
        
        print("\n--- Results (Left Arm Base Coordinates) ---")
        print(f"Gripping Point(Edge) Position (Left - Base): {edge_pts_left}")
        print("Gripping Orientation (Rotation Matrix - Left - Base):")
        print(grip_rot_left)

        # Calculate and print Euler angles from grip_rot_left
        try:
            r_grip_left = R_scipy.from_matrix(grip_rot_left)
            euler_angles_xyz_deg = r_grip_left.as_euler('xyz', degrees=True)
            print(f"Gripping Orientation (Euler Angles XYZ, degrees - Left - Base): {euler_angles_xyz_deg}")
        except Exception as e:
            print(f"Could not calculate Euler angles: {e}")

if __name__ == "__main__":
    main() 