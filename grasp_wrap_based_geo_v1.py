import cv2
import sys
import torch
import open3d as o3d
import numpy as np
import PIL.Image
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R_scipy

from utils import SAMTRTPredictor, YOLOv8TRTDetector, mask_to_point_cloud
from find_grasp_obb_align import calculate_gripping_pose_obb

def bbox2points(bbox):
    """Convert bounding box to points for SAM"""
    points = np.array([
        [bbox[0], bbox[1]],  # top-left
        [bbox[2], bbox[3]]   # bottom-right
    ])

    point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
    return points, point_labels

def draw_bbox(bbox, color='g'):
    """Draw bounding box on the plot"""
    x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
    y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
    plt.plot(x, y, color + '-')

def subplot_notick(a, b, c):
    """Create subplot without ticks"""
    ax = plt.subplot(a, b, c)
    ax.set_xticklabels([])
    ax.set_yticklabels([])
    ax.axis('off')


def main():
    # Path to files
    color_path = "data/img/2.png"
    depth_path = "data/img/2.tif"

    # config infer mask
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"device: {device}")
    yolo_engine = 'weights/yolov8s_basket.engine'
    image_encoder = 'weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'weights/mobile_sam_mask_decoder_fp16.engine'

    # Initialize pose estimator
    print("Initialize pose estimator")
    detector = YOLOv8TRTDetector(yolo_engine, device)
    sam_predictor = SAMTRTPredictor(device, image_encoder, mask_decoder)
    
    # yolo infer
    pil_image = PIL.Image.open(color_path).convert('RGB')
    cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    detections = detector.predict(cv_image)
   
    N = len(detections['bboxes'])
    
    if N == 0:
        print("No objects detected with the current settings.")
        sys.exit(0)

    bbox = detections['bboxes'][0]

    # sam infer
    points, point_labels = bbox2points(bbox)
    sam_predictor.set_image(pil_image)
    mask, _, _ = sam_predictor.predict(points, point_labels)
    mask = (mask[0, 0] > 0).detach().cpu().numpy()
    # mask = mask * 255
    mask = mask.astype(np.uint8) * 255

    if True:
        # pil_mask = PIL.Image.fromarray(mask, 'L')
        # print(f"pil_mask.size: {pil_mask.size}")
        # pil_mask.save("mask.png")

        subplot_notick(2, N, 1)
        plt.imshow(pil_image)
        draw_bbox(bbox)
        plt.title("Detection")

        subplot_notick(2, N, 2)
        plt.imshow(pil_image)
        plt.imshow(mask, alpha=0.5)
        plt.title("Segmentation Mask")
        plt.subplots_adjust(wspace=0.05, hspace=0.2)
        plt.savefig('out.png', bbox_inches="tight")
       
    # estimate pose
    rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
    depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
    depth = depth.astype(np.float32)    
    depth = depth / 1000.0   # scale to meter, mm -> m

    # generate point cloud from rgb and depth dependant on mask
    intrinsic = np.array([
        [647.4208984375, 0, 643.5106201171875],
        [0, 646.6436157226562, 360.1596374511719],
        [0, 0, 1]   
    ]) # color intrinsic
    # mask to point cloud
    object_pcd = mask_to_point_cloud(rgb, depth, mask, intrinsic)
    
    # --- Parameters ---
    # This factor is now relative to the OBB's estimated height dimension
    rim_threshold = 0.20 # Might need adjustment (e.g., 10% of OBB height)

    # --- Run Calculation ---
    _, grip_rot, edge_pts = calculate_gripping_pose_obb( # Call the new function
        object_pcd, 
        rim_height_threshold_factor=rim_threshold, 
        visualize=True, # Set to True to see the window
        output_path="out_grasp/out_sdk_img_intrinsic_20-2")

    if edge_pts is not None and grip_rot is not None:
        print("\n--- Results ---")
        # print(f"Gripping Point(Mid) Position (Camera): {grip_pos}")
        print(f"Gripping Point(Edge) Position (Camera): {edge_pts}")
        print("Gripping Orientation (Rotation Matrix - Camera):")
        print(grip_rot)
    else:
        print("\nFailed to calculate gripping pose.")

    camera_ext_param = np.array([
        [0.107116, 0.9941, 0.0170677, -0.138795],
        [-0.994209, 0.106946, 0.0105474, 0.0239795],
        [0.00865988, -0.0180987, 0.999799, 0.0404092],
        [0, 0, 0, 1]
    ])

    # Transform points and rotation from camera to world frame
    if edge_pts is not None and grip_rot is not None:
        R_w_c = camera_ext_param[:3, :3]
        t_w_c = camera_ext_param[:3, 3]

        # Transform edge points
        # edge_pts is to be Nx3
        edge_pts_w = (R_w_c @ edge_pts.T + t_w_c[:, np.newaxis]).T

        # Transform grip rotation
        grip_rot_w = R_w_c @ grip_rot
        
        print("\n--- Results (World Coordinates) ---")
        print(f"Gripping Point(Edge) Position (World): {edge_pts_w}")
        print("Gripping Orientation (Rotation Matrix - World):")
        print(grip_rot_w)

        # Calculate and print Euler angles from grip_rot_w
        try:
            r_grip_w = R_scipy.from_matrix(grip_rot_w)
            euler_angles_xyz_deg = r_grip_w.as_euler('xyz', degrees=True)
            print(f"Gripping Orientation (Euler Angles XYZ, degrees - World): {euler_angles_xyz_deg}")
        except Exception as e:
            print(f"Could not calculate Euler angles: {e}")
    
    
if __name__ == "__main__":
    main()
