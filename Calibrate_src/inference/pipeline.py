"""
抓取流程管道模块
从机器人驱动中分离出来的业务逻辑，实现抓取流程的控制
"""


def execute_grasp_pipeline(robot, camera=None, pose_estimator=None):
    """
    执行抓取流程
    
    Args:
        robot: 机器人驱动对象
        camera: 相机对象，可选
        pose_estimator: 姿态估计器对象，可选
    
    Returns:
        int: 执行结果，0表示成功
    """
    
    if camera is None or pose_estimator is None:
        # 如果没有相机和姿态估计器，执行预设的抓取流程
        RUNTIME = 2  # 设定loop次数
        curtime = 0
        while curtime < RUNTIME:
            curtime += 1
            print("Start loop pipeline, Pid: {}".format(curtime))
            robot.grasp_move(robot.pipelinepoint["grasppipeline"])
            print("Pid: {} , execute end !!!".format(curtime))
    else:
        # 使用相机和姿态估计器进行实时抓取
        while True:
            # 获取图像数据
            [_, colordata, pointsarr] = camera.get_image()
            pose_estimator(colordata, pointsarr)

            # pose_estimator.visualization()
            print(["##########   curobjnum", pose_estimator.getitemnum()])

            # 处理每个检测到的目标
            for index in range(0, pose_estimator.getitemnum()):
                curinstance = pose_estimator.getnextitem()

                if curinstance is None:
                    print("$$$$$$$$$$$ clear")
                    break

                # 复制抓取流程点
                pipelinepoints = robot.pipelinepoint["grasppipeline"].copy()

                # 调整抓取位置
                refixcurinstance = curinstance["obb_6Dof"]
                refixcurinstance[0] += robot.gripshift[0]
                refixcurinstance[1] += robot.gripshift[1]
                refixcurinstance[2] += robot.gripshift[2]

                pipelinepoints[1] = refixcurinstance

                # 执行抓取动作
                robot.grasp_move(pipelinepoints)

    # 复位机器人
    robot.reset()
    
    return 0
