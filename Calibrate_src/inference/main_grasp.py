from ..common.camera_driver import OrbCamera
from ..common.robot_driver import ur_robotic
from .pose_estimator import PoseEstimate
from .pipeline import execute_grasp_pipeline

def main():

    ### init variable
    urobj = ur_robotic("../configs/robot_config.json")
    orbcamera = OrbCamera()
    poseestimator = PoseEstimate("../configs/hand_eye_matrix.txt")

    execute_grasp_pipeline(urobj, orbcamera, poseestimator)
    # execute_grasp_pipeline(urobj)

    return 0


if __name__=="__main__":
    main()

