from pyorbbecsdk import Pipeline, Config, OBSensorType, OBAlignMode, OBFormat ,FrameSet,OBCameraParam
import numpy as np
import cv2
import open3d as o3d
import os

def convert_to_o3d_point_cloud(points, colors=None):
    """
    Converts numpy arrays of points and colors (if provided) into an Open3D point cloud object.
    """
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    if colors is not None:
        pcd.colors = o3d.utility.Vector3dVector(colors / 255.0)  # Assuming colors are in [0, 255]
    return pcd

class OrbCamera:
    def __init__(self,name='FemtoBolt'):

        self.savedir = None
        self.saveimgdir = None
        self.saveplydir =None

        # 初始化深度相机
        self.pipeline = Pipeline()
        device = self.pipeline.get_device()
        device_info = device.get_device_info()
        device_pid = device_info.get_pid()

        config = Config()
        
        ## color profile config
        profile_list = self.pipeline.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
        color_profile = profile_list.get_default_video_stream_profile()
        # color_profile = profile_list.get_video_stream_profile(3840, 2160, OBFormat.MJPG, 15)
        config.enable_stream(color_profile)

        ## depth profile configc
        profile_list = self.pipeline.get_stream_profile_list(OBSensorType.DEPTH_SENSOR)
        depth_profile = profile_list.get_default_video_stream_profile()
        #depth_profile = profile_list.get_video_stream_profile(1024, 1024, OBFormat.Y16, 15)
        config.enable_stream(depth_profile)

        print("color profile : {}x{}@{}_{}".format(color_profile.get_width(),
                                                   color_profile.get_height(),
                                                   color_profile.get_fps(),
                                                   color_profile.get_format()))
        print("depth profile : {}x{}@{}_{}".format(depth_profile.get_width(),
                                                   depth_profile.get_height(),
                                                   depth_profile.get_fps(),
                                                   depth_profile.get_format()))

        align_mode = 'SW' ## other model 'SW_MODEL', 'DISABLE'


        if align_mode == 'HW':
            config.set_align_mode(OBAlignMode.HW_MODE)
        elif align_mode =='SW':
            config.set_align_mode(OBAlignMode.SW_MODE)
        else:
            config.set_align_mode(OBAlignMode.DISABLE)

        self.pipeline.enable_frame_sync()
        self.pipeline.start(config)

    def __del__(self):
        self.pipeline.stop()

    def get_image(self,transnumpy=True):
        depth_data = None
        color_data = None
        pointcloud = None
        while True:
            frames = self.pipeline.wait_for_frames(100)
            if frames is None:
                continue

            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()
            points = frames.get_color_point_cloud(self.pipeline.get_camera_param())

            if color_frame is None or depth_frame is None or points is None:
                continue

            if transnumpy:

                width = depth_frame.get_width()
                height = depth_frame.get_height()
                scale = depth_frame.get_depth_scale()

                # 获取深度图像的原始数据
                depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
                depth_data = depth_data.reshape((height, width))
                depth_data = depth_data.astype(np.float32) * scale

                # 获取RGB图像的原始数据
                color_data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
                color_data = cv2.imdecode(color_data, cv2.IMREAD_COLOR)
                # color_data = cv2.cvtColor(color_data, cv2.COLOR_RGB2BGR)
                pointcloud = np.array(points[:, :3])
            else:
                depth_data = depth_frame
                color_data = color_frame
                pointcloud= points

            break

        return [depth_data,color_data,pointcloud]
    
    def set_images_save_dir(self,savedir):
        self.savedir = savedir
        self.saveimgdir = os.path.join(savedir,'images')
        self.savedepthdir = os.path.join(savedir,'depths')
        self.saveplydir = os.path.join(savedir,'plys')

        if not os.path.exists(self.savedir):
            os.mkdir(self.savedir)
            print("create calibrate dir")

        if not os.path.exists(self.saveimgdir):
            os.mkdir(self.saveimgdir)
            print("create calibrate images dir")

        if not os.path.exists(self.saveplydir):
            os.mkdir(self.saveplydir)
            print("create calibrate plys dir")
        
        if not os.path.exists(self.savedepthdir):
            os.mkdir(self.savedepthdir)
            print("create calibrate depth dir")

        return 0

    def get_images_save_dir(self):
        return self.savedir

    def save_color_and_pointscloud(self,index=None):

        if self.savedir is None:
            print("Please call 'self.set_images_save_dir()' at first")
            return 0

        framid = 0 
        while True:
            frames = self.pipeline.wait_for_frames(100)
            if frames is None:
                continue

            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()
            points = frames.get_color_point_cloud(self.pipeline.get_camera_param())


            if color_frame is None or depth_frame is None or points is None:
                continue
            
            if framid <10:
                framid+=1
                continue

            xyz = np.array(points[:, :3])
            colors = np.array(points[:, 3:], dtype=np.uint8)
            pcd = convert_to_o3d_point_cloud(xyz, colors)

            if index is not None:
                points_filename = os.path.join(self.saveplydir, f"color_points_{depth_frame.get_timestamp()}_{str(index)}.ply")
                depth_filename = os.path.join(self.savedepthdir, f"color_points_{depth_frame.get_timestamp()}_{str(index)}.tif")
                color_filename = os.path.join(self.saveimgdir , "color_{}_{}.png".format(color_frame.get_timestamp(),str(index)))

            else:
                points_filename = os.path.join(self.saveplydir, f"color_points_{depth_frame.get_timestamp()}.ply")
                depth_filename = os.path.join(self.savedepthdir, f"color_points_{depth_frame.get_timestamp()}.tif")
                color_filename = os.path.join(self.saveimgdir , "color_{}.png".format(color_frame.get_timestamp()))

            o3d.io.write_point_cloud(points_filename, pcd)

            color_data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
            color_data = cv2.imdecode(color_data, cv2.IMREAD_COLOR)


            width = depth_frame.get_width()
            height = depth_frame.get_height()
            scale = depth_frame.get_depth_scale()
            
            depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
            depth_data = depth_data.reshape((height, width))
            depth_data = depth_data.astype(np.float32) * scale
            

            cv2.imwrite(color_filename, color_data)
            cv2.imwrite(depth_filename, depth_data)
            break

        return 0



    def visualize(self,savedir=None):
        
        try:
            while True:
                try:
                    [depth_data,color_data,_] = self.get_image(transnumpy=True)

                    if depth_data is None or color_data is None:
                        continue

                    # 使用伪彩色映射器将深度数据转换为伪彩色图像
                    depth_colormap = cv2.normalize(depth_data, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
                    depth_colormap = cv2.applyColorMap(depth_colormap, cv2.COLORMAP_JET)
                    # 创建一个窗口，显示彩色图像和伪彩色深度图像
                    combined_image = np.hstack((color_data, depth_colormap))
                    cv2.imshow("Combined Image", combined_image)

                    # align_image = cv2.addWeighted(color_data, 0.5, depth_colormap, 0.5, 0)
                    # cv2.imshow("SyncAlignViewer ", align_image)
                    # 按Esc键退出循环
                    key = cv2.waitKey(1)
                    if key == 27:
                        break
                except RuntimeError as e:
                    print(f"等待帧时发生错误: {e}")

        finally:
            cv2.destroyAllWindows()

    def save_image(self,imgdir):
        pass


if __name__=="__main__":
    orb_camera = OrbCamera()

    orb_camera.visualize()

    # [depthdata,colordata] = orb_camera.get_image()

    # print(['depthdata shape:',depthdata.shape])
    # print(['colordata shape:',colordata.shape])

    # orb_camera.set_images_save_dir('./output/datacollect4')

    # import time
    # collecttime = 100
    # while collecttime:
    #     orb_camera.save_color_and_pointscloud()
    #     collecttime-=1
    #     time.sleep(1)

    #     print('change image!!!!!!!!!!!!!!!')



