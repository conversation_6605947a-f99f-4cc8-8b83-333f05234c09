import os
import pyrealsense2 as rs
import numpy as np
import cv2
import open3d as o3d
from utils import convert_to_o3d_point_cloud
import time


class IntelCamera:
    def __init__(self,name='D450i'):

        # 初始化深度相机
        self.pipeline = rs.pipeline()
        config = rs.config()
        config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
        
        self.savedir = None

        self.pipeline.start(config)

    def __del__(self):
        self.pipeline.stop()

    def get_image(self,transnumpy=True):
        frames = self.pipeline.wait_for_frames(timeout_ms=10000)
        depth_frame = frames.get_depth_frame()
        color_frame = frames.get_color_frame()

        aligned_pr = rs.align(rs.stream.color)
        aligned_frame = aligned_pr.process(frames)
        
        aligned_depth_frame = aligned_frame.get_depth_frame()
        
        colorized_depth = np.asanyarray(aligned_depth_frame.get_data())
        colorizer = rs.colorizer()
        colored_depth_frame = colorizer.colorize(aligned_depth_frame)
        colored_depth_image = np.asanyarray(colored_depth_frame.get_data())
 
        pc=rs.pointcloud()
        # 创建点云并可视化（使用Open3D）
        points = pc.calculate(aligned_depth_frame)

        # points = rs.points(frames) 
        # print(type(points))

        if transnumpy:

            if not depth_frame or not color_frame:
                return [None,None]

            # 获取深度图像的原始数据
            depth_data = np.asanyarray(depth_frame.get_data())
    
            # 获取RGB图像的原始数据
            color_data = np.asanyarray(color_frame.get_data())

            return [depth_data,color_data,points]

        return [depth_frame,color_frame,points]
    
    def get_images_save_dir(self):
        return self.savedir

    def set_images_save_dir(self,savedir):
        self.savedir = savedir
        self.saveimgdir = os.path.join(savedir,'images')
        self.saveplydir = os.path.join(savedir,'plys')

        if not os.path.exists(self.savedir):
            os.mkdir(self.savedir)
            print("create calibrate dir")

        if not os.path.exists(self.saveimgdir):
            os.mkdir(self.saveimgdir)
            print("create calibrate images dir")

        if not os.path.exists(self.saveplydir):
            os.mkdir(self.saveplydir)
            print("create calibrate plys dir")

        return 0

    def save_color_and_pointscloud(self,index=None):

        if self.savedir is None:
            print("Please call 'self.set_images_save_dir()' at first")
            return 0
        curindex=0

        while True:
            curindex+=1

            if curindex <1000:
                continue

            [depth_frame,color_frame,points] = self.get_image(transnumpy=True)

            if depth_frame is None or color_frame is None or points is None:
                continue
            # print([type(depth_frame),depth_frame.shape])

            vertexes = np.asanyarray(points.get_vertices()).view(np.float32).reshape(-1, 3)  # 获取顶点坐标
            colors = np.asanyarray(points.get_vertices()).view(np.uint8).reshape(-1, 6)  # 获取顶点颜色（如果有的话）
            # mesh = o3d.geometry.TriangleMesh()  #
            # mesh.vertex.set_from_vector(vertexes)  # 设置网格的顶点坐标。
            # mesh.vertex_colors = o3d.utility.Vector3dVector(colors)  # 设置网格的顶点颜色。注意：这里的颜色处理可能需要调整，因为直接从点云获取的颜色可能与预期不符。通常我们会通过纹理映射来为点云添加颜色。
            # mesh.compute_vertex_normals()  # 计算顶点法线（可选）


            pcd = convert_to_o3d_point_cloud(vertexes)


            if index is not None:
                points_filename = os.path.join(self.saveplydir, f"depth_{str(index)}.ply")
                color_filename = os.path.join(self.saveimgdir , "color_{}.png".format(str(index)))

            else:
                points_filename = os.path.join(self.saveplydir, f"depth_{curindex}.ply")
                color_filename = os.path.join(self.saveimgdir , "color_{}.png".format(curindex))

            # o3d.io.write_point_cloud(points_filename, pcd)
            # cv2.imwrite(points_filename,depth_frame)
            o3d.io.write_point_cloud(points_filename, pcd)
            # color_frame = cv2.cvtColor(color_frame, cv2.COLOR_BGR2RGB)
            cv2.imwrite(color_filename, color_frame)
            break
        return 0



    def visualize(self,savedir=None):
        
        colorizer = rs.colorizer()

        try:
            while True:
                try:
                    [depth_frame,color_frame] = self.get_image(transnumpy=False)

                    if not depth_frame or not color_frame:
                        continue

                    # 获取RGB图像的原始数据
                    color_data = np.asanyarray(color_frame.get_data())
    
                    # 使用伪彩色映射器将深度数据转换为伪彩色图像
                    depth_colormap = np.asanyarray(colorizer.colorize(depth_frame).get_data())
                    # 创建一个窗口，显示彩色图像和伪彩色深度图像
                    combined_image = np.hstack((color_data, depth_colormap))
                    cv2.imshow("Combined Image", combined_image)
 
                    # 按Esc键退出循环
                    key = cv2.waitKey(1)
                    if key == 27:
                        break
                except RuntimeError as e:
                    print(f"等待帧时发生错误: {e}")

        finally:
            cv2.destroyAllWindows()

    def save_image(self,imgdir):
        pass


if __name__=="__main__":
    irs_camera = IntelCamera()

    # irs_camera.visualize()

    # [depthdata,colordata] = irs_camera.get_image()

    # print(['depthdata shape:',depthdata.shape])
    # print(['colordata shape:',colordata.shape])

    irs_camera.set_images_save_dir('../../output/intelD450i-new2/')

    Index = 1
    while Index < 10:
        irs_camera.save_color_and_pointscloud(Index)
        Index += 1

        time.sleep(1)
        