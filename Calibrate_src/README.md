# Calibrate_src 项目

这是一个重构后的机器人手眼标定和抓取系统，包含标定和推理两个主要功能模块。

## 项目结构

```
Calibrate_src/
├── calibration/                # 标定模块
│   ├── __init__.py
│   ├── main_calibrate.py       # 标定入口程序
│   ├── algorithm.py            # 手眼标定算法
│   └── data/                   # 标定数据存储目录
│       └── README.md
│
├── inference/                  # 推理模块
│   ├── __init__.py
│   ├── main_grasp.py           # 抓取推理入口程序
│   ├── pipeline.py             # 抓取流程控制
│   ├── pose_estimator.py       # 姿态估计器
│   └── utils.py                # 工具函数
│
├── common/                     # 共享模块
│   ├── __init__.py
│   ├── robot_driver.py         # 机器人驱动（仅硬件控制）
│   └── camera_driver.py        # 相机驱动
│
├── configs/                    # 配置文件
│   ├── robot_config.json       # 机器人配置
│   └── hand_eye_matrix.txt     # 手眼标定结果矩阵
│
└── README.md                   # 项目说明文档
```

## 功能模块说明

### 1. 标定模块 (calibration/)
- **main_calibrate.py**: 标定数据收集的主入口程序
- **algorithm.py**: 实现圆形标定板的手眼标定算法
- **data/**: 存储标定过程中采集的图像和点云数据

### 2. 推理模块 (inference/)
- **main_grasp.py**: 抓取推理的主入口程序
- **pipeline.py**: 抓取流程控制逻辑，从硬件驱动中分离出来的业务逻辑
- **pose_estimator.py**: 基于点云的目标姿态估计
- **utils.py**: 姿态估计相关的工具函数

### 3. 共享模块 (common/)
- **robot_driver.py**: UR机器人的硬件驱动接口
- **camera_driver.py**: Orbbec相机的驱动接口

### 4. 配置模块 (configs/)
- **robot_config.json**: 机器人相关配置参数
- **hand_eye_matrix.txt**: 手眼标定得到的变换矩阵

## 使用方法

### 运行标定程序
```bash
cd Calibrate_src
python -m calibration.main_calibrate
```

### 运行抓取推理程序
```bash
cd Calibrate_src
python -m inference.main_grasp
```

## 重构改进

1. **模块化设计**: 将标定和推理功能完全分离，提高代码的可维护性
2. **解耦合**: 消除了硬件驱动与业务逻辑的强耦合
3. **统一配置**: 所有配置文件集中管理，便于维护
4. **清晰结构**: 建立了层次分明的目录结构，便于理解和扩展

## 依赖要求

- Python 3.x
- OpenCV
- Open3D
- NumPy
- scikit-learn
- transforms3d
- pyorbbecsdk
- ur_rtde

## 注意事项

1. 运行前请确保机器人和相机硬件连接正常
2. 配置文件中的IP地址和参数需要根据实际硬件环境调整
3. 标定结果矩阵文件 `hand_eye_matrix.txt` 需要通过标定程序生成后才能用于推理
