# TODO: 重构 `Calibrate_src` 子项目

**版本:** 1.0
**日期:** 2025-06-30

---

## 1. 当前代码结构问题分析

当前 `Calibrate_src` 目录下的代码结构存在职责混杂和高耦合的问题，不利于长期维护和功能扩展。

- **职责混杂**: 目录名称为 `Calibrate_src`，但其内部不仅包含了标定功能 (`calibrate_datacollection.py`, `Calibrate/`)，还包含了一个完整的推理和抓取应用 (`main.py`, `grasppose/`)。
- **高耦合度**: 
  - 推理模块 (`grasppose/pcSement3d.py`) 直接依赖于标定模块的内部文件路径 (`./Calibrate/Tnew.txt`) 来获取变换矩阵。这违反了模块独立性原则。
  - 应用层逻辑 (`run_grasp_pipeline`) 被错误地放置在了硬件驱动层 (`robotic/ur_robotic_driver.py`) 中，导致硬件驱动与业务逻辑强耦合。
- **结构不清晰**: 所有组件（标定、推理、驱动、配置）散落在根目录，缺乏清晰的层次结构，增加了新成员理解代码的难度。

---

## 2. 重构目标

- **模块化**: 将标定和推理功能彻底分离，成为两个独立的组件。
- **解耦**: 消除不合理的跨模块文件依赖，通过统一的配置接口进行交互。
- **高内聚**: 将硬件驱动、配置文件等通用组件集中管理，提高代码的内聚性。
- **可维护性**: 建立一个清晰、可预测的目录结构，方便未来新增功能（如支持新相机、新机器人）或进行维护。

---

## 3. 重构后的目录结构建议

```
Calibrate_src/
├── calibration/
│   ├── __init__.py
│   ├── main_calibrate.py       # <--- 原 calibrate_datacollection.py，标定入口
│   ├── algorithm.py            # <--- 原 Calibrate/circlehandeyecalibration.py
│   └── data/                   # <--- 原 output/ 目录，存放采集数据
│       └── README.md
│
├── inference/
│   ├── __init__.py
│   ├── main_grasp.py           # <--- 原 main.py，推理入口
│   ├── pipeline.py             # <--- (新建) 存放抓取流程逻辑 (从ur_robotic_driver.py中剥离)
│   └── pose_estimator.py       # <--- 原 grasppose/pcSement3d.py
│
├── common/
│   ├── __init__.py
│   ├── robot_driver.py         # <--- 原 robotic/ur_robotic_driver.py (只保留硬件控制)
│   └── camera_driver.py        # <--- 原 camera/op_orbbec_femto_bolt.py 等
│
├── configs/
│   ├── robot_config.json       # <--- 原 robotic/ur_config.json
│   └── hand_eye_matrix.txt     # <--- 原 Calibrate/Tnew.txt，统一的标定结果
│
└── README.md                   # <--- (新建) 说明整个子项目
```

---

## 4. 重构实施步骤（含优先级）

### 优先级 P0: 结构准备

1.  **[P0] 备份项目**: 在进行重构前，务必完整备份 `Calibrate_src` 目录。 (已完成)
2.  **[P0] 创建新目录**: 在 `Calibrate_src/` 下创建 `calibration`, `inference`, `common`, `configs` 四个新目录。
3.  **[P0] 创建 `__init__.py`**: 在所有新创建的包目录（`calibration`, `inference`, `common`）中创建空的 `__init__.py` 文件，使其成为Python可识别的包。

### 优先级 P1: 文件迁移与重命名

1.  **[P1] 移动配置文件**:
    - `mv Calibrate_src/robotic/ur_config.json Calibrate_src/configs/robot_config.json`
    - `mv Calibrate_src/Calibrate/Tnew.txt Calibrate_src/configs/hand_eye_matrix.txt`
2.  **[P1] 移动并重命名标定代码**:
    - `mv Calibrate_src/calibrate_datacollection.py Calibrate_src/calibration/main_calibrate.py`
    - `mv Calibrate_src/Calibrate/circlehandeyecalibration.py Calibrate_src/calibration/algorithm.py`
    - `mv Calibrate_src/output Calibrate_src/calibration/data`
3.  **[P1] 移动并重命名推理代码**:
    - `mv Calibrate_src/main.py Calibrate_src/inference/main_grasp.py`
    - `mv Calibrate_src/grasppose/pcSement3d.py Calibrate_src/inference/pose_estimator.py`
4.  **[P1] 移动并重命名共享驱动**:
    - `mv Calibrate_src/robotic/ur_robotic_driver.py Calibrate_src/common/robot_driver.py`
    - `mv Calibrate_src/camera/op_orbbec_femto_bolt.py Calibrate_src/common/camera_driver.py`

### 优先级 P2: 代码解耦与修改

1.  **[P2] 解耦抓取流程**:
    - 在 `Calibrate_src/inference/pipeline.py` 中新建文件。
    - 将 `common/robot_driver.py` 中的 `run_grasp_pipeline` 方法的**全部代码**剪切并粘贴到 `inference/pipeline.py` 中，定义为一个新的函数，例如 `def execute_grasp_pipeline(robot, camera, pose_estimator): ...`。
    - 从 `common/robot_driver.py` 中**删除** `run_grasp_pipeline` 方法。
2.  **[P2] 修改配置加载路径**:
    - **修改 `inference/pose_estimator.py`**: 将 `PoseEstimate` 类中加载变换矩阵的硬编码路径 `./Calibrate/Tnew.txt` 修改为 `../configs/hand_eye_matrix.txt` 或通过参数传入。
    - **修改 `calibration/main_calibrate.py` 和 `inference/main_grasp.py`**: 将加载机器人配置的路径 `./robotic/ur_config.json` 修改为 `../configs/robot_config.json`。
3.  **[P2] 更新所有 `import` 语句**:
    - 逐一检查所有被移动的 `.py` 文件。
    - 将旧的导入语句（如 `from robotic.ur_robotic_driver import ur_robotic`）更新为新的结构（如 `from common.robot_driver import ur_robotic`）。
    - 特别注意 `inference/main_grasp.py`，它现在需要导入 `inference.pipeline` 中的新函数。

### 优先级 P3: 清理与文档

1.  **[P3] 删除旧目录**: 在重构完成后，删除空的旧目录，如 `Calibrate_src/robotic`, `Calibrate_src/camera`, `Calibrate_src/grasppose`, `Calibrate_src/Calibrate`。
2.  **[P3] 编写文档**:
    - 创建 `Calibrate_src/README.md`，简要说明新结构、如何运行标定 (`python -m calibration.main_calibrate`) 以及如何运行抓取 (`python -m inference.main_grasp`)。
    - 在 `calibration/data/README.md` 中说明采集的数据格式。

---

## 5. 预期收益与风险评估

### 预期收益

- **结构清晰**: 开发者可以快速定位标定、推理或硬件驱动的相关代码。
- **易于维护**: 修改相机驱动不会影响推理逻辑；调整抓取流程也无需触及底层驱动。
- **高可扩展性**:
  - 新增相机型号时，只需在 `common/` 目录下添加新的驱动文件。
  - 新增标定算法时，只需在 `calibration/` 目录下添加新算法文件。
- **关注点分离**: 实现了业务逻辑、硬件控制和配置文件的分离，符合现代软件工程标准。

### 风险评估

- **高风险**: **导入路径错误**。这是重构中最常见的风险。在 P2 阶段需要进行大量的路径更新和测试，任何一条遗漏都可能导致程序无法运行。
- **中风险**: **业务逻辑剥离不彻底**。在解耦 `run_grasp_pipeline` 时，可能存在一些隐藏的对 `ur_robotic` 类内部状态的依赖，需要仔细审查并将其作为参数传递。
- **低风险**: **配置文件管理**。统一管理配置文件后，需要确保所有模块都使用正确的相对路径进行访问，避免出现 `FileNotFoundError`。

**缓解措施**:
- **Code Review**: 邀请其他团队成员审查重构后的代码，特别是 `import` 语句和配置加载部分。
- **版本控制**: 强烈建议在独立的 Git 分支上进行重构，以便在出现严重问题时可以轻松回退。 