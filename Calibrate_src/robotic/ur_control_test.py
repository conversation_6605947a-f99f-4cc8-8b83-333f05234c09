import logging
import sys
import rtde_control
import rtde_receive
from utils import rpy2rotvec

# ROBOT_HOST = "**************"
# ROBOT_HOST = "*************" # UR - right 
ROBOT_HOST = "*************" # UR - left 


rtde_c = rtde_control.RTDEControlInterface(ROBOT_HOST)
rtde_r = rtde_receive.RTDEReceiveInterface(ROBOT_HOST)

tcp_pose = rtde_r.getActualTCPPose()
print("TCP位置和姿态：", tcp_pose)


new_tcp_pose = tcp_pose

pose = [1,0,0]   # pose: degrees
rotvec = rpy2rotvec(pose)

print(rotvec)

new_tcp_pose[3] += rotvec[0]
new_tcp_pose[4] += rotvec[1]
new_tcp_pose[5] += rotvec[2]

velocity = 0.1
acceleration = 0.1
rtde_c.moveL(new_tcp_pose, velocity, acceleration) # [x, y, z, Rx, Ry, Rz] rad





