import os
import json

import rtde_control
import rtde_receive

import numpy as np
import time
import random

import socket


class ur_robotic:
    def __init__(self,jsonfile):

        self.pipelinepoint = None

        with open(jsonfile, "r", encoding="utf-8") as f:
            pipelinedata = json.load(f)
            self.pipelinepoint = pipelinedata

        # print(self.pipelinepoint)

        # ROBOT_HOST = "**************"
        ROBOT_HOST = "*************"  # left 25; right 11

        self.rtde_c = rtde_control.RTDEControlInterface(ROBOT_HOST)
        self.rtde_r = rtde_receive.RTDEReceiveInterface(ROBOT_HOST)
        print("robotic linked!")

        # PORT = 63352 #robotiq grspper 使用的端口

        # self.robotiq = socket.socket(socket.AF_INET,socket.SOCK_STREAM)

        # self.robotiq.connect((ROBOT_HOST,PORT))

        # self.robotiq.sendall(b'SET ACT 1\n') #完成激活
        # self.robotiq.sendall(b'SET GTO 0\n') #重置末端动作
        # self.robotiq.sendall(b'SET MOD 0\n') #设置优先模式
        # print("夹爪链接成功")

        self.velocity =0.1
        self.acceleration =0.1

        self.gripshift = [-0.00761,0.00991,0.15298]


    def get_cur_pose(self):
        tcp_pose = self.rtde_r.getActualTCPPose()
        return tcp_pose


    def set_cur_pose(self,newpose):
        self.rtde_c.moveL(newpose, self.velocity, self.acceleration)
        time.sleep(3)

        return newpose
        

    def move_linear_by_idx(self,idx,movedistance=0.05):

        tcp_pose = self.get_cur_pose()
        tcp_pose[idx] +=movedistance
        self.set_cur_pose(tcp_pose)

        return 0
    
    def move_x(self, movedistance=0.05):

        self.move_linear_by_idx(0,movedistance)
        return 0


    def move_y(self, movedistance=0.05):

        self.move_linear_by_idx(1,movedistance)
        return 0


    def move_z(self, movedistance=0.05):

        self.move_linear_by_idx(2,movedistance)
        return 0

    def move_x_rad(self, movedistance=0.05):

        self.move_linear_by_idx(3,movedistance)
        return 0

    def move_y_rad(self, movedistance=0.05):

        self.move_linear_by_idx(4,movedistance)
        return 0
    def move_z_rad(self, movedistance=0.05):

        self.move_linear_by_idx(5,movedistance)
        return 0
    
    def loop_by_config(self):

        pass
        return 0 
    
    def build_calibrate_pose(self):

        centerpose = self.get_cur_pose()

        poselist = [centerpose.copy() for index in range(27)]


        for index in range(1,27):
            x_shift = random.random() *0.3 - 0.15
            y_shift = random.random() *0.3 - 0.15
            z_shift = random.random() *0.25 - 0.1

            x_rot =random.random() *1.4 - 0.7
            y_rot =random.random() *1.4 - 0.7
            z_rot =random.random() *1.4 - 0.7


            poselist[index][0] += x_shift
            poselist[index][1] += y_shift
            poselist[index][2] += z_shift
            poselist[index][3] += x_rot
            poselist[index][4] += y_rot
            poselist[index][5] += z_rot


        return poselist
        
    def gripperOFF(self):
        self.robotiq.sendall(b'SET POS 0\n') #设置关闭位置
        self.robotiq.sendall(b'SET GTO 1\n') #进行运动 

    def gripperOn(self):
        self.robotiq.sendall(b'SET POS 255\n') #设置张开位置
        self.robotiq.sendall(b'SET GTO 1\n') #进行运动 


    def Calibrate_data_collection(self,cameraobj,calibrate_file):

        if calibrate_file is not None:
            posearr = np.loadtxt(calibrate_file)
            posearr[:,:3]*=0.001
            poselist = posearr.tolist()

        else:
            poselist = self.build_calibrate_pose()


        for index in range(0,len(poselist)):

            curpose = poselist[index]

            self.set_cur_pose(curpose)

            if cameraobj is not None:
                cameraobj.save_color_and_pointscloud(index+1)

            else:
                print(curpose)
                time.sleep(1)

        if cameraobj.get_images_save_dir() is not None:

            posearr = np.array(poselist)
            posearr[:,:3]*=1000
            saveposefile = os.path.join(cameraobj.get_images_save_dir(),'pose.txt')
            np.savetxt(saveposefile,posearr)

        return 0

    def reset(self):
        self.set_cur_pose(self.pipelinepoint['sourcepoint'])
        

    def grasp_move(self,pipelinepoint):
        ## 机械臂复位
        self.reset()
        print(pipelinepoint)
        for index in range(0,len(pipelinepoint)):
            curpose = pipelinepoint[index]

            self.set_cur_pose(curpose)

            if index == 1:
                ## grasp signal true
                self.gripperOn()
                print("grasp success !")

            if index == 4:
                ## grasp signal false
                self.gripperOFF()
                print("putting success !")

        return 0


    def run_grasp_pipeline(self,cameraobj=None,poseestimator=None):

        if cameraobj is None or poseestimator is None:

            RUNTIME = 2 ## 设定loop次数
            curtime = 0
            while curtime < RUNTIME:
                curtime+=1
                print("Start loop  pipeline, Pid: {}".format(curtime))
                self.grasp_move(self.pipelinepoint["grasppipeline"])
                print("Pid: {} , execute end !!!".format(curtime))
        else:

            while True:

                [_,colordata,pointsarr] = cameraobj.get_image()
                poseestimator(colordata,pointsarr)

                # poseestimator.visualization()
                print(["##########   curobjnum",poseestimator.getitemnum()])

                for index in range(0,poseestimator.getitemnum()):
                    curinstance = poseestimator.getnextitem()

                    if curinstance is None:

                        print("$$$$$$$$$$$ clear")
                        break

                    pipelinepoints = self.pipelinepoint["grasppipeline"].copy()


                    refixcurinstance = curinstance["obb_6Dof"]
                    refixcurinstance[0]+=self.gripshift[0]
                    refixcurinstance[1]+=self.gripshift[1]
                    refixcurinstance[2]+=self.gripshift[2]

                    pipelinepoints[1] = refixcurinstance

                    self.grasp_move(pipelinepoints)

         
        ## 复位
        self.reset()
        
        return 0



if __name__=="__main__":

    urobj = ur_robotic("./ur_config.json")

    # tcp_pose = urobj.get_cur_pose()

    # print(tcp_pose)

    # urobj.Calibrate_data_collection("../../output/Calibrate5/pose.txt",None)
    poselist = urobj.build_calibrate_pose()

    print(poselist)

    for index in range(0,len(poselist)):

        print(poselist[index])
        urobj.set_cur_pose(poselist[index])

        time.sleep(3)


