import json
import numpy as np

def load_json(jsonfile):
    '''
    读取配置文件，json以dictionary的形式返回
    '''
    data = None
    with open(jsonfile, "r", encoding="utf-8") as f:
        data = json.load(f)

    return data

def save_json(dictdata,outputfile):

    jsonstring = json.dumps(dictdata,indent=4)

    with open(outputfile,'w') as f:
        f.write(jsonstring)

    return 0

def save_txt(dataarr,outputfile):

    with open(outputfile,'w')as f:
        for row in dataarr:
            line  = ' '.join(map(str,row))+'\n'

            f.write(line)

    return 0



#将欧拉角转为旋转矢量
def rpy2rotvec(pose):
    gamma, beta, alpha = pose

    ca = np.cos(alpha)
    cb = np.cos(beta)
    cg = np.cos(gamma)
    sa = np.sin(alpha)
    sb = np.sin(beta)
    sg = np.sin(gamma)

    rotation_matrix = np.zeros((3, 3))

    rotation_matrix[0, 0] = ca * cb
    rotation_matrix[0, 1] = ca * sb * sg - sa * cg
    rotation_matrix[0, 2] = ca * sb * cg + sa * sg
    rotation_matrix[1, 0] = sa * cb
    rotation_matrix[1, 1] = sa * sb * sg + ca * cg
    rotation_matrix[1, 2] = sa * sb * cg - ca * sg
    rotation_matrix[2, 0] = -sb
    rotation_matrix[2, 1] = cb * sg
    rotation_matrix[2, 2] = cb * cg

    theta = np.arccos((rotation_matrix[0, 0] + rotation_matrix[1, 1] + rotation_matrix[2, 2] - 1) / 2)
    sth = np.sin(theta)
    if sth == 0:
        return np.zeros(3)
    kx = (rotation_matrix[2, 1] - rotation_matrix[1, 2]) / (2 * sth)
    ky = (rotation_matrix[0, 2] - rotation_matrix[2, 0]) / (2 * sth)
    kz = (rotation_matrix[1, 0] - rotation_matrix[0, 1]) / (2 * sth)

    rovetc = np.zeros(3)

    rovetc[0] = theta * kx
    rovetc[1] = theta * ky
    rovetc[2] = theta * kz

    return rovetc

