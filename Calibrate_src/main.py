from camera.op_orbbec_femto_bolt import OrbCamera
from robotic.ur_robotic_driver import ur_robotic
from grasppose.pcSement3d import PoseEstimate

def main():

    ### init variable
    urobj = ur_robotic("./robotic/ur_config.json")
    orbcamera = OrbCamera()
    poseestimator = PoseEstimate("./Calibrate/Tnew.txt")   

    urobj.run_grasp_pipeline(orbcamera,poseestimator)
    # urobj.run_grasp_pipeline()

    return 0


if __name__=="__main__":
    main()

