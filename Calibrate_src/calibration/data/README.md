# 标定数据目录

此目录用于存储手眼标定过程中采集的数据。

## 数据结构

标定数据按照以下结构组织：

```
data/
├── Calibrate22_right/          # 标定数据集目录
│   ├── images/                 # RGB图像
│   │   ├── color_1.png
│   │   ├── color_2.png
│   │   └── ...
│   ├── plys/                   # 点云数据
│   │   ├── points_1.ply
│   │   ├── points_2.ply
│   │   └── ...
│   └── pose.txt                # 机器人姿态数据
└── README.md                   # 本说明文件
```

## 数据格式说明

### 1. RGB图像 (images/)
- 格式: PNG
- 命名规则: `color_{序号}.png`
- 内容: 标定板在不同位置的彩色图像

### 2. 点云数据 (plys/)
- 格式: PLY
- 命名规则: `points_{序号}.ply`
- 内容: 对应位置的3D点云数据

### 3. 机器人姿态 (pose.txt)
- 格式: 文本文件，每行一个姿态
- 内容: 机器人末端执行器的6D姿态 [x, y, z, rx, ry, rz]
- 单位: 位置单位为毫米，旋转单位为弧度

## 数据采集流程

1. 机器人移动到预设的标定位置
2. 相机采集当前位置的RGB图像和点云数据
3. 记录机器人当前的末端执行器姿态
4. 重复上述步骤，采集多个位置的数据

## 注意事项

- 确保标定板在每个位置都清晰可见
- 标定位置应该覆盖相机的工作空间
- 建议采集至少10-15个不同位置的数据以获得良好的标定精度
