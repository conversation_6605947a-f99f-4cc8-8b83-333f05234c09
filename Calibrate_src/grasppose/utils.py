import cv2
import numpy as np
import open3d as o3d
import math

## ============   variables =================== ##


lookup_table =  {-1:[1.0,0.8,0.4],0:[0.,1.,1.],1:[1.0,0.,1.],2:[1.0,0.2,0.],3:[1.0,0.4,1.],4:[1.0,0.6,0.],16:[1.0,0.8,1.],\
                5:[0.5,0.,0.1],6:[0.0,0.,0.3],7:[0.5,0.,0.5],8:[0.0,0.,0.7],\
                9:[0.5,0.,0.9],10:[0.0,0.5,0.1],11:[0.5,0.5,0.3],12:[0.0,0.5,0.5],13:[0.5,0.5,0.7],14:[0.0,0.5,0.9],\
                15:[0.5,1.0,0.1],17:[0.0,1.0,0.3],18:[0.5,1.0,0.5],19:[0.0,1.0,0.7],20:[0.5,1.0,0.9]}





## ============   functions =================== ##

def draw_bbox_in_2dimg(img, bbox):
    """
    Draws a bounding box on an image.
    Args:
        img (numpy.ndarray): The input image.
        bbox (list or tuple): Bounding box coordinates in the format [x_min, y_min, x_width, y_height].
    
    Returns:
        numpy.ndarray: Image with the bounding box drawn.
    """
    # Unpack the bounding box coordinates
    x_min, y_min, x_width, y_height = bbox
    x_max = x_min+x_width
    y_max = y_min+y_height

    ptLeftTop = (x_min,y_min) 
    ptRightBottom = (x_max,y_max) 

    point_color = (0, 255, 0) # BGR

    thickness = 10 
    lineType = 4

    # Draw the bounding box on the image
    cv2.rectangle(img, ptLeftTop, ptRightBottom, point_color, thickness, lineType)
    cv2.imshow("test",imagedata)

    cv2.waitKey(0)
    cv2.destroyWindow("test")

    return None

def convert_to_o3d_point_cloud(points, colors=None):
    """
    Converts numpy arrays of points and colors (if provided) into an Open3D point cloud object.
    """
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    if colors is not None:
        pcd.colors = o3d.utility.Vector3dVector(colors / 255.0)  # Assuming colors are in [0, 255]
    return pcd

def colored_geometries_by_label(point_clouds, seglabellist):
    """
    根据几何对象对点云进行着色
    :param point_clouds: list of open3d.geometry.PointCloud
    :return: None
    """
    pcdlist = []

    seglabelarr = np.array(seglabellist)
    seglabels = np.unique(seglabelarr)


    for index in range(len(seglabels)):
        pcd = o3d.geometry.PointCloud()
        curlabelpoints = point_clouds[seglabelarr == seglabels[index]]
        pcd.points = o3d.utility.Vector3dVector(curlabelpoints[:,:3])
        pcd.paint_uniform_color(lookup_table[seglabels[index]])
        pcdlist.append(pcd)

    o3d.visualization.draw_geometries(pcdlist)
    return 0

def draw_geometries_vis_open3d(pcdlist,savename=None):
    """
    使用open3d可视化点云
    :param pcdlist: list of open3d.geometry.PointCloud
    :return: None
    """
    if savename is not None:
        vis = o3d.visualization.Visualizer()
        vis.create_window(window_name=savename,visible=False)
        for pcd in pcdlist:
            vis.add_geometry(pcd)

        ctr = vis.get_view_control()
        ctr.set_lookat(np.array([0.0, 0.0, 500.0]))
        ctr.set_up((1.7, 1.7, 0)) 
        ctr.set_front((0.3, 0, -1))

        vis.poll_events()
        vis.update_renderer()
        vis.capture_screen_image(savename)
        vis.destroy_window()
    else:
        o3d.visualization.draw_geometries(pcdlist)


def rot2euler(R):
 
    sy = math.sqrt(R[0, 0] * R[0, 0] + R[1, 0] * R[1, 0])
 
    singular = sy < 1e-6
 
    if not singular:
        x = math.atan2(R[2, 1], R[2, 2])
        y = math.atan2(-R[2, 0], sy) 
        z = math.atan2(R[1, 0], R[0, 0])
    else:
        x = math.atan2(-R[1, 2], R[1, 1])
        y = math.atan2(-R[2, 0], sy)
        z = 0
 
    return np.array([x, y, z])

def rot2rotvec(R):
    theta = np.arccos((R[0,0]+R[1,1]+R[2,2]-1)/2.)
    sth = np.sin(theta)
    if sth == 0:
        return np.zeros(3)
    
    kx=(R[2,1]-R[1,2] )/(2*sth)
    ky=(R[0,2]-R[2,0] )/(2*sth)
    kz=(R[1,0]-R[0,1] )/(2*sth)

    rovetc = np.zeros(3)

    rovetc[0] = theta*kx
    rovetc[1] = theta*ky
    rovetc[2] = theta*kz

    return rovetc

def translate_boxes_to_open3d_instance(gt_boxes,color):
    """
             6-------- 5
           /|         /|
          7 -------- 4 .
          | |        | |
          . 2 -------- 1
          |/         |/
          3 -------- 0
    """
    center = gt_boxes[0:3]
    lwh = gt_boxes[3:6]
    axis_angles = np.array([0, 0, gt_boxes[6] + 1e-10])
    rot = o3d.geometry.get_rotation_matrix_from_axis_angle(axis_angles)
    box3d = o3d.geometry.OrientedBoundingBox(center, rot, lwh)
    box3d.color = color

    ##增加坐标轴，可视化检查yaw角
    axis_pcd = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.5, origin=center)
    axis_pcd = axis_pcd.rotate(rot,center)

    return box3d,axis_pcd

def calculate_bbox_v2(pointArray):
    """
    Oriented Bounding Box (OBB) 
    https://blog.csdn.net/juluwangriyue/article/details/128812950

    使用x，y平面计算 obb，z轴使用原始的空间

    """
    xypoints = pointArray[:,:2]

    minz = pointArray[:,2].min()
    maxz = pointArray[:,2].max()

    ca = np.cov(xypoints,y = None,rowvar = 0,bias = 1)
    v, vect = np.linalg.eig(ca)
    tvect = np.transpose(vect)
    #use the inverse of the eigenvectors as a rotation matrix and
    #rotate the points so they align with the x and y axes
    ar = np.dot(xypoints,np.linalg.inv(tvect))
    
    # get the minimum and maximum x and y 
    mina = np.min(ar,axis=0)
    maxa = np.max(ar,axis=0)
    diff = (maxa - mina)*0.5
    # the center is just half way between the min and max xy
    center = mina + diff
    
    #get the 8 corners by subtracting and adding half the bounding boxes height and width to the center
    cornersxy = np.array([center+[-diff[0],-diff[1]],
                        center+[diff[0],-diff[1]],
                        center+[diff[0],diff[1]],
                        center+[-diff[0],diff[1]]])
    
    #use the the eigenvectors as a rotation matrix and
    #rotate the corners and the centerback
    cornersxy = np.dot(cornersxy,tvect)
    center = np.dot(center,tvect)

    corners = np.array([[cornersxy[0][0],cornersxy[0][1],minz],
                        [cornersxy[1][0],cornersxy[1][1],minz],
                        [cornersxy[2][0],cornersxy[2][1],minz],
                        [cornersxy[3][0],cornersxy[3][1],minz],
                        [cornersxy[0][0],cornersxy[0][1],maxz],
                        [cornersxy[1][0],cornersxy[1][1],maxz],
                        [cornersxy[2][0],cornersxy[2][1],maxz],
                        [cornersxy[3][0],cornersxy[3][1],maxz],
                        ])

    zradius = (maxz-minz)
    centerz = minz + zradius*0.5
    length = [diff[0]*2,diff[1]*2,zradius]
    centerxyz = [center[0],center[1],centerz]
    
    aabbquaternion,aabbeuler,rotvec = bounding_box_properties(corners)

    yaw = aabbeuler[2]
    return corners, centerxyz, length, yaw,rotvec


def draw_bbox_by_corners(corners,color):

    boxlines = [[0, 1], [1, 2], [2, 3], [3, 0], [4, 5], [5, 6], [6, 7],
                      [7, 4], [0, 4], [1, 5], [2, 6], [3, 7],[1,6],[2,5]]

    lines = o3d.geometry.LineSet()
    lines.points = o3d.utility.Vector3dVector(corners)
    lines.lines = o3d.utility.Vector2iVector(boxlines)
    lines.paint_uniform_color(color)

    return lines

from scipy.spatial.transform import Rotation as R

def bounding_box_properties(corners):
        """
        Calculate the center, dimensions, and orientation of a bounding box given its 8 corner points in 3D space.
        https://github.com/marcopalena/convex/blob/5d01d23c89fffcca4efd80497889594fe8bef5c6/lib/bboxes.py#L34

        Parameters:
            corners (list or numpy.ndarray): A (8, 3) array representing the 3D coordinates of the bounding box corners,
                assumed to be ordered such that edges are aligned with principal axes (RBL, FBL, FBR, RBR, RTL, FTL, FTR, RTR).

        Returns:
            center (numpy.ndarray): A (3,) array representing the center of the bounding box.
            dimensions (numpy.ndarray): A (3,) array representing the width, height, and depth of the bounding box.
            quaternion (numpy.ndarray): A (4,) array representing the orientation of the bounding box as a quaternion (x, y, z, w).
        """

        # Convert corners to a NumPy array for easier manipulation
        corners = np.array(corners)
        if corners.shape != (8, 3):
            raise ValueError("Input must be an (8, 3) array representing the corner points of the bounding box.")

        # Compute the vectors representing the edges of the box
        edges = np.array([
            corners[1] - corners[0],  # Edge from rear bottom left corner to front bottom left corner ()
            corners[3] - corners[0],  # Edge from rear bottom left corner to rear bottom right corner
            corners[4] - corners[0],  # Edge from rear bottom left corner to rear top left corner
        ])

        # Normalize the edge vectors to find the principal axes
        principal_axes = np.array([edge / np.linalg.norm(edge) for edge in edges])

        # The orientation is represented by the rotation matrix formed by the principal axes
        rotation_matrix = np.column_stack(principal_axes)

        # Convert the rotation matrix to a quaternion
        quaternion = R.from_matrix(rotation_matrix).as_quat()
        euler = R.from_matrix(rotation_matrix).as_euler('xyz')
        rotvec = R.from_matrix(rotation_matrix).as_rotvec()
        
        basematrix = R.from_matrix(rotation_matrix)
        gripmatrix = R.from_euler('xyz', [180, 0, 0], degrees=True)
        
        gripvect = (basematrix*gripmatrix).as_rotvec()
        print(["gripvect",gripvect])
        print(["euler",euler])

        # Return bounding-box center, dimensions and orientation
        return quaternion,euler,gripvect

