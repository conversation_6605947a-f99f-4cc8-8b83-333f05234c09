import os
import cv2
import open3d as o3d
import numpy as np
from sklearn.cluster import DBSCAN
import time

from .utils import draw_bbox_in_2dimg,colored_geometries_by_label,\
    lookup_table,draw_geometries_vis_open3d,rot2euler,rot2rotvec,calculate_bbox_v2,translate_boxes_to_open3d_instance

"""
姿态估计类，用于对点云目标进行姿态估计

DONE:
[20250418] 完成基于聚类和平面估计的工件分割以及外接矩形框提取

TODO:
    1. 姿态估计算法，包括旋转和平移矩阵的计算，输出参数供机械臂及机械抓工作
    2. 抓取策略优化，基于姿态估计结果选择最优抓取点

"""
class PoseEstimate:
    def __init__(self,CalibrateT=None):
        ## 定义工作区，可根据实际的工装进行调整
        self.workareabox = [400,80,880,700]  

        ## 定义工作平面参数，初始化，在实际估计当中更新
        self.plane_model = None
        self.plane_points = None
        
        ## 存储实例信息，在示例估计之后更新
        self.instances = []
        
        # 当前抓取目标的id
        self.getitemid = 0

        self.loadTransmat(CalibrateT)


    def loadTransmat(self,CalibrateT):
        if type(CalibrateT) is str:
            CalibrateT = np.loadtxt(CalibrateT)
        self.CalibrateT = CalibrateT

    def markworkareabox(self,imagedata):
        """
        工作区标记，用于可视化
        :param imagedata: 输入图像数据
        :return: None 仅用于显示标记结果，检查输出
        """
        draw_bbox_in_2dimg(imagedata,self.workareabox)  # 调用函数绘制边界框


    def cropworkarea(self,imagedata,pointsarr):
        """
        裁剪工作区，用于后续处理
        :param imagedata: 输入图像数据 numpy array
        :param pcdata: 输入点云数据 numpy array
        :return: 裁剪后的图像和点云数据
        """        
        ## 创建mask数据，裁剪image
        maskdata = np.zeros_like(imagedata)
        
        ## opencv 中的xy坐标，与矩阵中的行和列相反
        maskdata[self.workareabox[1]:(self.workareabox[1]+self.workareabox[3]),\
                self.workareabox[0]:(self.workareabox[0]+self.workareabox[2]),:] = 1

        self.workimage = imagedata*maskdata  ## for chcek， 后续可用于目标检测（基于深度学习的）

        imagepcdata = pointsarr.reshape(imagedata.shape[0],imagedata.shape[1],3)

        self.workpcpoints = imagepcdata[self.workareabox[1]:(self.workareabox[1]+self.workareabox[3]),\
                self.workareabox[0]:(self.workareabox[0]+self.workareabox[2]),:]

        return 0

    def buildground(self):
        """
        方案：根据点云建立抓取平面模型，ax+by+cz+d=0，通过该平面参数，可以计算前景点云
        1. 采用DBSCAN算法对裁剪工作区进行聚类，获取最大联通区域，被视为工作台
        2. 基于RANSAC算法，估计工作台平面模型参数
        3. 根据平面模型参数，计算前景点云

        return: 前景点云数组，平面点云数组，平面模型参数[a,b,c,d]
        """

        ## 对空间点云数据进行网格采样, 加速计算
        sampleworkpcpoint = self.workpcpoints[::5,::5,:].reshape(-1,3)

        ## 基于open3d的dbscan算法聚类时间长
        # labels = np.array(partpcd.cluster_dbscan(eps=20, min_points=10, print_progress=True))
        labels = DBSCAN(eps=20, min_samples=40).fit(sampleworkpcpoint).labels_  # 只使用xyz坐标进行聚类

        # colored_geometries_by_label(sampleworkpcpoint, labels)

        ## 获取聚类结果中最大的类别，为操作台上的点云
        unique, counts = np.unique(labels, return_counts=True)
        max_label = unique[np.argmax(counts)]

        # 获取操作台上的点云
        table_points = sampleworkpcpoint[labels == max_label]

        # colored_geometries_by_label(table_points, labels[labels == max_label])
        
        ## 调用open3d的平面分割算法，获得平面参数以及内点索引
        samplepcd = o3d.geometry.PointCloud()
        samplepcd.points = o3d.utility.Vector3dVector(table_points)
        plane_model, inliers = samplepcd.segment_plane(distance_threshold= 5,
                                         ransac_n=3,
                                         num_iterations=1000)

        plane_points = table_points[inliers]
        object_points = table_points[~np.in1d(np.arange(table_points.shape[0]),inliers)]

        # nosamplepoints = self.workpcpoints.reshape(-1,3)

        # distances = nosamplepoints*np.array(plane_model[:3]) + plane_model[3]

        # object_pointsindex = np.where(distances>5)[0]

        # object_points = nosamplepoints[object_pointsindex]
        # plane_points = nosamplepoints[~np.in1d(np.arange(nosamplepoints.shape[0]),object_pointsindex)]

        return object_points,plane_points,plane_model


    def instancesegmentation(self,object_points):
        """
        对前景点云进行聚类，获取不同的目标，过滤并进行属性计算

        """

        # 实例聚类，将点云中的不同物体进行区分 DBSACN的聚类参数， eps代表同一个簇中两点的最大距离，单位为mm，因为我们做了采样，所以这里设置为20mm
        labels = DBSCAN(eps=20, min_samples=40).fit(object_points).labels_  # 只使用xyz坐标进行聚类
       
        # colored_geometries_by_label(object_points, labels)

        ## 获取当前所有聚类的标签和对应的数量
        uniqueslabel,countslist = np.unique(labels,return_counts=True)
        
        self.instances = [] ## 首先清空实例列表

        iid = 0
        for index in range(0,len(uniqueslabel)):

            ## 过滤掉小的聚类结果，认为是噪声或者干扰物   # 噪声点的标签为-1，过滤掉噪声点和小聚类结果
            if countslist[index] <40 or uniqueslabel[index] == -1:
                continue
            else:
                instancepoints = object_points[labels == uniqueslabel[index]]

                ## 校准点云坐标，如果存在校准矩阵，则进行坐标变换
                if self.CalibrateT is not None:
                    # instancepoints = (np.linalg.inv(self.CalibrateT) @ np.vstack((instancepoints.T, np.ones(instancepoints.shape[0]))))[:3].T
                    instancepoints = self.CalibrateT.dot(np.vstack((instancepoints.T, np.ones(instancepoints.shape[0]))))[:3].T

                    instancepoints*=0.001

                print(["iid",iid])
                ## 构建此刻的实例
                curinstance = {"iid":iid}
                curinstance["samplepoints"] = instancepoints
                mean_point = np.mean(instancepoints, axis=0)

                curinstance["mean_point"] = mean_point.tolist()
                curinstancepcd =  o3d.geometry.PointCloud() 
                curinstancepcd.points = o3d.utility.Vector3dVector(instancepoints)
                curinstancepcd.colors = o3d.utility.Vector3dVector([lookup_table[index]]*countslist[index])
                curinstance["pcd"] = curinstancepcd

                corners, centerxyz, length, yaw,griprotvect = calculate_bbox_v2(instancepoints)
                curinstance["obb"],curinstance["axis"]  = translate_boxes_to_open3d_instance(np.array([centerxyz[0],centerxyz[1],centerxyz[2],\
                                                                                             length[0],length[1],length[2],yaw]),[0,1,0])
                
                curinstance["obb_6Dof"] =  [centerxyz[0],centerxyz[1],centerxyz[2],griprotvect[0],griprotvect[1],griprotvect[2]]
                
                mesh = o3d.t.geometry.TriangleMesh.create_text('instance_id_{}'.format(iid), depth=1)
                mesh.compute_triangle_normals()
                mesh.compute_vertex_normals()
                
                curinstance["text3d"] = mesh.to_legacy().paint_uniform_color(lookup_table[index])
                curinstance["text3d"].translate((mean_point[0], mean_point[1], mean_point[2]+0.5))

                self.instances.append(curinstance)

                iid+=1

        return 0

    def getitemnum(self):
        return len(self.instances)


    def getnextitem(self):
        ## 输出当前目标，供机器人进行抓取
        if self.getitemid <self.getitemnum():
            curinstance = self.instances[self.getitemid]
            self.getitemid +=1
        else:
            return None

        return curinstance

    def __call__(self,imagedata,pcdata):
        self.getitemid = 0
        ## 1. 目标区域的裁剪
        self.cropworkarea(imagedata,pcdata)
        ## 可视化查看目标区域大小
        # self.markworkareabox(self.workimage)

        ## 2. 工作平面估计
        object_points,self.plane_points,self.plane_model  = self.buildground()
        

        ## 3. 前景提取并聚类，实例ID输出
        self.instancesegmentation(object_points)


    def visualization(self,savename=None):
        ## 可视化结果，离线/在线检查
        pcdlist = []
        for index in range(0,self.getitemnum()):
            curinstance = self.instances[index]

            if curinstance is None:
                break

            pcdlist.append( curinstance['pcd'])
            pcdlist.append( curinstance['obb'])
            pcdlist.append( curinstance['axis'])
            pcdlist.append( curinstance["text3d"])
        
        planpoints = self.CalibrateT.dot(np.vstack((self.plane_points.T, np.ones(self.plane_points.shape[0]))))[:3].T



        planepcd =  o3d.geometry.PointCloud() 
        planepcd.points = o3d.utility.Vector3dVector(planpoints*0.001)
        planepcd.colors = o3d.utility.Vector3dVector([[0,0,0.3]]*len(planpoints))
        pcdlist.append(planepcd)
        
        ## 调用utils中的可视化函数
        draw_geometries_vis_open3d(pcdlist,savename=savename)



def offline_check_data(inputdir,outputdir):
    ## build with 

    imagedir = os.path.join(inputdir,'images')
    plydir = os.path.join(inputdir,'plys')

    imagelist = os.listdir(imagedir)
    plylist = os.listdir(plydir)


    poseestimator = PoseEstimate()   

    for index in range(0,len(imagelist)):

        curimg = imagelist[index]
        curply = plylist[index]

        print(["input:",curimg,curply])

        savename = os.path.join(outputdir,"vis_"+curimg)

        imagedata = cv2.imread(os.path.join(imagedir,curimg))
        pcdatafile = os.path.join(plydir,curply)
        pcdataarr = np.asarray(o3d.io.read_point_cloud(pcdatafile).points)

        poseestimator(imagedata,pcdataarr)
        poseestimator.visualization(savename)    
    
        print(["save image:",savename])
    return 0 

if __name__=="__main__":

    imagedata = cv2.imread("D:\sharefolder\datacollect\images\color_120519983.png")
    # poseestimator.markworkareabox(imagedata)

    pcdatafile = "D:\sharefolder\datacollect\plys\color_points_120519984.ply"

    savename = '../../output/color_points_120519984.png'


    pcdata = o3d.io.read_point_cloud(pcdatafile)

    poseestimator = PoseEstimate("../Calibrate/T.txt")   
    poseestimator(imagedata,np.asarray(pcdata.points))
    poseestimator.visualization()    
    
    # inputdir = r"D:\sharefolder\datacollect"
    # outputdir = r"../../output"
    # offline_check_data(inputdir,outputdir)
