import numpy as np
import cv2
import os
from transforms3d import euler


class CircleHandEyeCalibration:
    def __init__(self):
        pass

    def execute(self, circle_pts, robotPoses, calibrateType="ETH"):
        data_pairs, mask = self._makeDataPairs(circle_pts, robotPoses, calibrateType)
        T = self._calcTransformationFromDataPairs(data_pairs)
        circle_pt, err = self._calcReprojErrFromRobotPtsStd(T, circle_pts, robotPoses, calibrateType)
        return T, circle_pt, err

    def _pose2T(self, pose):
        # R = euler.euler2mat(np.deg2rad(pose[3]), np.deg2rad(pose[4]), np.deg2rad(pose[5]), axes='sxyz')
        R = euler.euler2mat(pose[3], pose[4], pose[5], axes='sxyz')
        t = pose[:3]
        T = np.eye(4, 4, dtype=float)
        T[0:3, 0:3] = R
        T[:3, -1] = t
        return T

    def _makeDataPairs(self, ptsInImageList, robotPoses, calibrate_type):
        data_pairs = []
        mask = [[0]] * len(ptsInImageList)
        for i in range(len(ptsInImageList)):
            for k in range(i+1, len(ptsInImageList)):
                pair_data = {}
                pt_in_image_1 = ptsInImageList[i]
                pt_in_image_2 = ptsInImageList[k]

                if calibrate_type == 'ETH':
                    T_tr_1 = np.linalg.inv(self._pose2T(robotPoses[i]))
                    T_tr_2 = np.linalg.inv(self._pose2T(robotPoses[k]))
                else:
                    T_tr_1 = self._pose2T(robotPoses[i])
                    T_tr_2 = self._pose2T(robotPoses[k])


                x_delta = abs(robotPoses[k][0] - robotPoses[i][0])
                y_delta = abs(robotPoses[k][1] - robotPoses[i][1])
                u_delta = abs(robotPoses[k][5] - robotPoses[i][5])

                if (x_delta + y_delta <= 1e-3) or (u_delta) <= 0.1:
                    # print('1: ', robotPoses[k][0], robotPoses[k][1], robotPoses[k][3])
                    # print('2: ', robotPoses[i][0], robotPoses[i][1], robotPoses[i][3])
                    # print('no')
                    continue
                else:
                    pair_data['pt_in_image_1'] = pt_in_image_1
                    pair_data['pt_in_image_2'] = pt_in_image_2
                    pair_data['T_tr_1'] = T_tr_1
                    pair_data['T_tr_2'] = T_tr_2
                    data_pairs.append(pair_data)
                    mask[i] = [1]
                    mask[k] = [1]

        if len(data_pairs) < 5:
            raise Exception('valid data not enough!')

        return data_pairs, mask

    def _calcTransformationFromDataPairs(self, dataPairs):

        matrix_A = []
        matrix_B = []

        for data_pair in dataPairs:
            X1, Y1, Z1 = data_pair["pt_in_image_1"]
            X2, Y2, Z2 = data_pair["pt_in_image_2"]
            T1 = data_pair['T_tr_1']
            T2 = data_pair['T_tr_2']

            T1_11, T1_12, T1_13, T1_14, T1_21, T1_22, T1_23, T1_24, T1_31, T1_32, T1_33, T1_34 = \
                T1[:3].flatten()
            T2_11, T2_12, T2_13, T2_14, T2_21, T2_22, T2_23, T2_24, T2_31, T2_32, T2_33, T2_34 = \
                T2[:3].flatten()

            matrix_A.append([
                T1_11 * X1 - T2_11 * X2,
                T1_11 * Y1 - T2_11 * Y2,
                T1_11 * Z1 - T2_11 * Z2,
                T1_11 - T2_11,
                T1_12 * X1 - T2_12 * X2,
                T1_12 * Y1 - T2_12 * Y2,
                T1_12 * Z1 - T2_12 * Z2,
                T1_12 - T2_12,
                T1_13 * X1 - T2_13 * X2,
                T1_13 * Y1 - T2_13 * Y2,
                T1_13 * Z1 - T2_13 * Z2,
                T1_13 - T2_13
            ])
            matrix_B.append([T2_14 - T1_14])

            matrix_A.append([
                T1_21 * X1 - T2_21 * X2,
                T1_21 * Y1 - T2_21 * Y2,
                T1_21 * Z1 - T2_21 * Z2,
                T1_21 - T2_21,
                T1_22 * X1 - T2_22 * X2,
                T1_22 * Y1 - T2_22 * Y2,
                T1_22 * Z1 - T2_22 * Z2,
                T1_22 - T2_22,
                T1_23 * X1 - T2_23 * X2,
                T1_23 * Y1 - T2_23 * Y2,
                T1_23 * Z1 - T2_23 * Z2,
                T1_23 - T2_23
            ])
            matrix_B.append([T2_24 - T1_24])

            matrix_A.append([
                T1_31 * X1 - T2_31 * X2,
                T1_31 * Y1 - T2_31 * Y2,
                T1_31 * Z1 - T2_31 * Z2,
                T1_31 - T2_31,
                T1_32 * X1 - T2_32 * X2,
                T1_32 * Y1 - T2_32 * Y2,
                T1_32 * Z1- T2_32 * Z2,
                T1_32 - T2_32,
                T1_33 * X1 - T2_33 * X2,
                T1_33 * Y1 - T2_33 * Y2,
                T1_33 * Z1 - T2_33 * Z2,
                T1_33 - T2_33
            ])
            matrix_B.append([T2_34 - T1_34])

        A = np.array(matrix_A)
        B = np.array(matrix_B)

        try:
            T_list = np.linalg.solve(((A.T).dot(A)), ((A.T).dot(B)))
        except Exception:
            raise Exception('data invalid or not enough, note that pose x, y, u should vary while calibration!')

        T = np.eye(4, 4, dtype=float)
        T[:3] = np.array(T_list).reshape(3, 4)
        return T

    def _calcReprojErrFromRobotPtsStd(self, T, ptsInImageList, robotPoses, calibrateType):
        # Treal = [
        #     0.00455761, 0.997576, -0.0694339, 0.42321,
        #     0.999133, -0.00167, 0.0415893, 0.33465,
        #     0.0413726, -0.0695632, -0.996719, 1.03328,
        #     0, 0, 0, 1
        # ]
        # Treal = np.array(Treal).reshape(4, 4)
        # T = Treal
        assert len(ptsInImageList) == len(robotPoses), 'Image number isn\'t equal with robot poses\'s'
        if calibrateType == "ETH":
            pts_in_tool_list = []
            for pt_in_image, robot_pos in zip(ptsInImageList, robotPoses):
                pt_in_image_homo = np.vstack((np.array(pt_in_image).reshape(3, 1), [1]))
                pt_in_robot_list = (T.dot(pt_in_image_homo)).tolist()
                pt_in_robot_homo = pt_in_robot_list
                pt_in_tool_list = np.linalg.inv(self._pose2T(robot_pos)).dot(pt_in_robot_homo).tolist()
                pts_in_tool_list.append(pt_in_tool_list)

            mean_pt = np.mean(pts_in_tool_list, axis=0)
            std = np.linalg.norm(np.std(pts_in_tool_list, axis=0))
            print(np.linalg.norm(pts_in_tool_list - mean_pt, axis=1))

        else:
            pts_in_robot_list = []
            for pt_in_image, robot_pos in zip(ptsInImageList, robotPoses):
                pt_in_image_homo = np.vstack((np.array(pt_in_image).reshape(3, 1), [1]))
                pt_in_tool_list = T.dot(pt_in_image_homo).tolist()
                pt_in_tool_homo = pt_in_tool_list
                pt_in_robot_list = (self._pose2T(robot_pos).dot(pt_in_tool_homo)).tolist()
                pts_in_robot_list.append(pt_in_robot_list)
            mean_pt = np.mean(pts_in_robot_list, axis=0)
            std = np.linalg.norm(np.std(pts_in_robot_list, axis=0))
        return mean_pt, std


if __name__ == '__main__':
    img_pts = np.loadtxt('../output/Calibrate22_right/1.txt')[:, :3]
    poses = np.loadtxt('../output/Calibrate22_right/1.txt')[:, 3:]
    # img_pts = np.loadtxt('center_pts.txt')
    # poses = np.loadtxt('poses.txt')

    calibrator = CircleHandEyeCalibration()
    T, circle_pt, err = calibrator.execute(img_pts, poses)
    print("T: ", T)
    print("circle_pt: ", circle_pt)
    print('err: ', err)
    # Treal = [
    #     0.00455761, 0.997576, -0.0694339, 0.42321,
    #     0.999133, -0.00167, 0.0415893, 0.33465,
    #     0.0413726, -0.0695632, -0.996719, 1.03328
    # ]

