import os
import time
import sys
import cv2
import torch
import open3d as o3d
import numpy as np
import PIL.Image
import matplotlib.pyplot as plt
import pyrealsense2 as rs

from utils import SAMTRTPredictor, YOLOv8TRTDetector, mask_to_point_cloud
from find_grasp_base_align import calculate_gripping_pose_base


class RealTimeGraspEstimator:
    def __init__(self, 
                 yolo_engine='weights/yolov8s_basket.engine',
                 image_encoder='weights/resnet18_image_encoder_fp16.engine',
                 mask_decoder='weights/mobile_sam_mask_decoder_fp16.engine',
                 device=None,
                 skip_frames=5,
                 rim_threshold=0.20,
                 visualization=True,
                 T_world = np.eye(4),
                 output_dir="out_grasp"):
        """
        Initialize the real-time grasp estimation system.
        
        Args:
            yolo_engine: Path to the YOLOv8 TensorRT engine
            image_encoder: Path to the SAM image encoder TensorRT engine
            mask_decoder: Path to the SAM mask decoder TensorRT engine
            device: Device to run inference on ('cuda' or 'cpu')
            skip_frames: Number of frames to skip between pose estimations
            rim_threshold: Threshold for rim height detection
            visualization: Whether to visualize results
            T_world: Transformation matrix from camera to world coordinates
            output_dir: Directory to save output files
        """
        # Initialize parameters
        self.skip_frames = skip_frames
        self.rim_threshold = rim_threshold
        self.visualization = visualization
        self.T_world = T_world
        self.output_dir = output_dir
        self.frame_count = 0
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Set device
        if device is None:
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            self.device = device
        
        print(f"Using device: {self.device}")
        
        # Initialize models
        print("Initializing pose estimator...")
        self.detector = YOLOv8TRTDetector(yolo_engine, self.device)
        self.sam_predictor = SAMTRTPredictor(self.device, image_encoder, mask_decoder)
        
        # Initialize camera
        print("Initializing RealSense camera...")
        self.pipeline = rs.pipeline()
        self.config = rs.config()
        self.config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        self.config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
        
        # Get camera intrinsics
        self.pipeline.start(self.config)
        frames = self.pipeline.wait_for_frames()
        color_frame = frames.get_color_frame()
        self.intrinsics = color_frame.profile.as_video_stream_profile().intrinsics
        self.intrinsic_matrix = np.array([
            [self.intrinsics.fx, 0, self.intrinsics.ppx],
            [0, self.intrinsics.fy, self.intrinsics.ppy],
            [0, 0, 1]
        ])
        
        # Set up alignment
        self.align_to = rs.stream.color
        self.align = rs.align(self.align_to)
        
        # Initialize result variables
        self.grip_pos = None
        self.grip_rot = None
        self.edge_pts = None
        self.last_detection_time = 0
        
        print("System initialization complete.")

    def bbox2points(self, bbox):
        """Convert bounding box to points for SAM"""
        points = np.array([
            [bbox[0], bbox[1]],  # top-left
            [bbox[2], bbox[3]]   # bottom-right
        ])

        point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
        return points, point_labels

    def draw_bbox(self, img, bbox, color=(0, 255, 0), thickness=2):
        """Draw bounding box on the image"""
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(img, (x1, y1), (x2, y2), color, thickness)
        return img

    def process_frame(self, color_data, depth_data):
        """
        Process a single frame to estimate grasp pose.
        
        Args:
            color_data: RGB image as numpy array
            depth_data: Depth image as numpy array
            
        Returns:
            grip_pos: Gripping position
            grip_rot: Gripping orientation
            edge_pts: Edge points of the grasp
            processed_image: Visualization image
        """
        # Convert BGR to RGB for PIL
        rgb_data = cv2.cvtColor(color_data, cv2.COLOR_BGR2RGB)
        pil_image = PIL.Image.fromarray(rgb_data)
        
        # Convert depth to meters
        depth_meters = depth_data.astype(np.float32) / 1000.0
        
        # Run YOLO detection
        detections = self.detector.predict(color_data)
        bboxes = detections['bboxes']
        
        # Process the result for visualization
        processed_image = color_data.copy()
        
        # Check if any objects detected
        if len(bboxes) == 0:
            print("No objects detected.")
            cv2.putText(processed_image, "No objects detected", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            return None, None, None, processed_image
        
        # Take the first detected object
        bbox = bboxes[0]
        
        # Draw bounding box
        processed_image = self.draw_bbox(processed_image, bbox)
        
        # Generate mask with SAM
        points, point_labels = self.bbox2points(bbox)
        self.sam_predictor.set_image(pil_image)
        mask, _, _ = self.sam_predictor.predict(points, point_labels)
        mask = (mask[0, 0] > 0).detach().cpu().numpy()
        mask_uint8 = mask.astype(np.uint8) * 255
        
        # Visualize mask on image
        mask_overlay = processed_image.copy()
        mask_rgb = cv2.cvtColor(mask_uint8, cv2.COLOR_GRAY2BGR)
        mask_rgb = np.where(mask_rgb > 0, (0, 255, 0), (0, 0, 0))
        mask_overlay = cv2.addWeighted(mask_overlay, 1, mask_rgb, 0.5, 0)
        
        # Generate point cloud from RGB and depth dependent on mask
        object_pcd = mask_to_point_cloud(rgb_data, depth_meters, mask_uint8, self.intrinsic_matrix)
        
        # Calculate gripping pose
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(self.output_dir, f"grasp_{timestamp}")
        
        grip_pos, grip_rot, edge_pts = calculate_gripping_pose_base(
            object_pcd, 
            rim_height_threshold_factor=self.rim_threshold,
            visualize=False,  # We'll visualize in our own way
            output_path=output_path,
            camera_extrinsic=self.T_world
        )
        
        # Update results if valid
        if grip_pos is not None and grip_rot is not None:
            self.grip_pos = grip_pos
            self.grip_rot = grip_rot
            self.edge_pts = edge_pts
            self.last_detection_time = time.time()
            
            # Add grip visualization to the image
            cv2.putText(mask_overlay, "Grasp detected", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Display edge points coordinates
            pt1, pt2 = edge_pts
            info_text = f"Edge pts: [{pt1[0]:.2f}, {pt1[1]:.2f}, {pt1[2]:.2f}], [{pt2[0]:.2f}, {pt2[1]:.2f}, {pt2[2]:.2f}]"
            cv2.putText(mask_overlay, info_text, (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        else:
            cv2.putText(mask_overlay, "No valid grasp found", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        # Return processed image for visualization
        return grip_pos, grip_rot, edge_pts, mask_overlay

    def run(self):
        """Run the real-time grasp estimation system"""
        try:
            print("Starting real-time grasp estimation...")
            while True:
                try:
                    # Wait for aligned frames
                    frames = self.pipeline.wait_for_frames(timeout_ms=5000)
                    aligned_frames = self.align.process(frames)
                    
                    # Get aligned depth and color frames
                    depth_frame = aligned_frames.get_depth_frame()
                    color_frame = aligned_frames.get_color_frame()
                    
                    if not depth_frame or not color_frame:
                        print("Failed to get frames, retrying...")
                        continue
                    
                    # Convert to numpy arrays
                    depth_data = np.asanyarray(depth_frame.get_data())
                    color_data = np.asanyarray(color_frame.get_data())
                    
                    # Process frame if we've skipped enough frames
                    if self.frame_count % self.skip_frames == 0:
                        grip_pos, grip_rot, edge_pts, vis_image = self.process_frame(color_data, depth_data)
                        
                        # Display results
                        cv2.imshow("Grasp Estimation", vis_image)
                        
                        # Print grasp information if available
                        if grip_pos is not None and grip_rot is not None:
                            print(f"\n--- Grasp Estimation Results (Frame {self.frame_count}) ---")
                            print(f"Gripping Position: {grip_pos}")
                            print(f"Edge Points: {edge_pts}")
                            print("Rotation Matrix:")
                            print(grip_rot)
                    else:
                        # Just show the color image with previous results if available
                        vis_image = color_data.copy()
                        if self.grip_pos is not None:
                            elapsed = time.time() - self.last_detection_time
                            cv2.putText(vis_image, f"Last grasp: {elapsed:.1f}s ago", (10, 30), 
                                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                        
                            # Display edge points
                            pt1, pt2 = self.edge_pts
                            info_text = f"Edge pts: [{pt1[0]:.2f}, {pt1[1]:.2f}, {pt1[2]:.2f}], [{pt2[0]:.2f}, {pt2[1]:.2f}, {pt2[2]:.2f}]"
                            cv2.putText(vis_image, info_text, (10, 90), 
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                        
                        cv2.imshow("Grasp Estimation", vis_image)
                    
                    # Increment frame counter
                    self.frame_count += 1
                    
                    # Check for key press
                    key = cv2.waitKey(1)
                    if key == 27:  # ESC key
                        break
                    elif key == ord('s'):  # Save current frame and results
                        timestamp = time.strftime("%Y%m%d_%H%M%S")
                        cv2.imwrite(f"{self.output_dir}/frame_{timestamp}.png", vis_image)
                        if self.grip_pos is not None:
                            np.savez(f"{self.output_dir}/grasp_{timestamp}.npz", 
                                     position=self.grip_pos, 
                                     rotation=self.grip_rot, 
                                     edge_points=self.edge_pts)
                            print(f"Saved current grasp to {self.output_dir}/grasp_{timestamp}.npz")
                
                except RuntimeError as e:
                    print(f"Error getting frames: {e}")
                    continue
                    
        finally:
            # Clean up
            self.pipeline.stop()
            cv2.destroyAllWindows()
            print("Real-time grasp estimation stopped.")

    def get_latest_results(self):
        """Get the latest grasp estimation results"""
        return self.grip_pos, self.grip_rot, self.edge_pts


def main():
    # Default parameters
    yolo_engine = 'weights/yolov8s_basket.engine'
    image_encoder = 'weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'weights/mobile_sam_mask_decoder_fp16.engine'
    
    # Create and run the estimator
    grasp_estimator = RealTimeGraspEstimator(
        yolo_engine=yolo_engine,
        image_encoder=image_encoder,
        mask_decoder=mask_decoder,
        skip_frames=10,  # Process every 10th frame
        rim_threshold=0.20,
        visualization=True,
        output_dir="out_grasp"
    )
    
    grasp_estimator.run()


if __name__ == "__main__":
    main()
