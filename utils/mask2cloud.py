import open3d as o3d
import cv2
import numpy as np

def mask_to_point_cloud(rgb_image, depth_image, mask, intrinsic):
    """
    Convert masked RGB and depth images to a colored point cloud.
    
    Args:
        rgb_image (np.ndarray): RGB image
        depth_image (np.ndarray): Depth image
        mask (np.ndarray): Binary mask of the object
        
    Returns:
        o3d.geometry.PointCloud: Colored point cloud of the object
    """
    # set o3d intrinsics
    o3d_intrinsic = o3d.camera.PinholeCameraIntrinsic()
    o3d_intrinsic.intrinsic_matrix = intrinsic

    # Apply mask to the RGB and depth images
    masked_rgb = rgb_image.copy()
    masked_depth = depth_image.copy()
    
    # Set background to black in RGB and 0 in depth
    for i in range(3):  # For each RGB channel
        masked_rgb[:, :, i] = np.where(mask, masked_rgb[:, :, i], 0)
    
    masked_depth = np.where(mask, masked_depth, 0)
    
    # Convert to Open3D format
    rgb_o3d = o3d.geometry.Image(masked_rgb)
    depth_o3d = o3d.geometry.Image(masked_depth)
    
    # Create RGBD image
    rgbd_image = o3d.geometry.RGBDImage.create_from_color_and_depth(
        rgb_o3d, depth_o3d, 
        depth_scale=1,  # Adjust based on the depth scale (mm or m) mm: 1000, m: 1
        depth_trunc=5.0,     # Maximum depth in meters
        convert_rgb_to_intensity=False
    )
    
    # Create point cloud from RGBD image
    pcd = o3d.geometry.PointCloud.create_from_rgbd_image(
        rgbd_image, o3d_intrinsic
    )
    
    # Remove zero points (where depth was 0)
    points = np.asarray(pcd.points)
    colors = np.asarray(pcd.colors)
    
    # Find points with non-zero coordinates (sum of absolute values > 0)
    valid_indices = np.where(np.abs(points).sum(axis=1) > 0)[0]
    
    # Create a new point cloud with only valid points
    object_pcd = o3d.geometry.PointCloud()
    object_pcd.points = o3d.utility.Vector3dVector(points[valid_indices])
    object_pcd.colors = o3d.utility.Vector3dVector(colors[valid_indices])

    # save object pcd
    o3d.io.write_point_cloud("filter_pcd_2.ply", object_pcd)
    
    return object_pcd

if __name__ == "__main__":
    image_path = "aligned_images/color.png" 
    depth_path = "aligned_images/aligned_depth.png"
    mask_path = "mask.png"
    # read rgb image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # read depth image
    depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
    print(f"depth.shape: {depth.shape}")
    depth = depth.astype(np.float32)    
    depth = depth / 1000.0   # scale to meter, mm -> m

    # read mask image
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    # mask = np.ones(image.shape[:2], dtype=np.uint8) * 255

    # build camera intrinsic matrix - color
    # intrinsic = np.array([
    #     [606.3263549804688, 0, 316.7557067871094],
    #     [0, 606.0850219726562, 245.0094451904297],
    #     [0, 0, 1]   
    # ]) # realsense D435i color instrinsic
    # intrinsic = np.array([
    #     [647.4208984375, 0, 643.5106201171875],
    #     [0, 646.6436157226562, 360.1596374511719],
    #     [0, 0, 1]   
    # ])  # D455 instrinsic
    # intrinsic = np.array([
    #     [1118.06, 0, 950.691],
    #     [0, 1117.72, 526.351],
    #     [0, 0, 1]
    # ]) # orb depth instrinsic default  1920x1080
    intrinsic = np.array([
        [1118.06, 0, 950.691],
        [0, 1117.72, 526.351],
        [0, 0, 1]
    ]) # orb coloe instrinsic default  1920x1080
    # mask to point cloud
    object_pcd = mask_to_point_cloud(image, depth, mask, intrinsic)
    print("done!")