from pyorbbecsdk import Pipeline, Config, OBSensorType, OBAlignMode, OBFormat, FrameSet, OBCameraParam, AlignFilter, OBStreamType, PointCloudFilter, save_point_cloud_to_ply
import numpy as np
import cv2
import open3d as o3d
import time
import os

def convert_to_o3d_point_cloud(points, colors=None):
    """
    Converts numpy arrays of points and colors (if provided) into an Open3D point cloud object.
    """
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    if colors is not None:
        # pcd.colors = o3d.utility.Vector3dVector(colors / 255.0)  # Assuming colors are in [0, 255]
        pcd.colors = o3d.utility.Vector3dVector(colors)  # Assuming colors are in [0, 255]
    return pcd


class OrbCamera:
    def __init__(self, name='FemtoBolt'):

        self.savedir = None
        self.saveimgdir = None
        self.saveplydir =None

        # initalize depth camera
        self.pipeline = Pipeline()
        device = self.pipeline.get_device()
        device_info = device.get_device_info()
        device_pid = device_info.get_pid()

        config = Config()
        
        ## color profile config
        profile_list = self.pipeline.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
        color_profile = profile_list.get_default_video_stream_profile()
        # color_profile = profile_list.get_video_stream_profile(640, 480, OBFormat.RGB, 30)
        
        config.enable_stream(color_profile)

        ## depth profile config
        profile_list = self.pipeline.get_stream_profile_list(OBSensorType.DEPTH_SENSOR)
        depth_profile = profile_list.get_default_video_stream_profile()
        config.enable_stream(depth_profile)

        print("color profile : {}x{}@{}_{}".format(color_profile.get_width(),
                                                   color_profile.get_height(),
                                                   color_profile.get_fps(),
                                                   color_profile.get_format()))
        print("depth profile : {}x{}@{}_{}".format(depth_profile.get_width(),
                                                   depth_profile.get_height(),
                                                   depth_profile.get_fps(),
                                                   depth_profile.get_format()))

        align_mode = 'SW' ## other model 'SW_MODEL', 'DISABLE'

        if align_mode == 'HW':
            config.set_align_mode(OBAlignMode.HW_MODE)
        elif align_mode =='SW':
            config.set_align_mode(OBAlignMode.SW_MODE)
        else:
            config.set_align_mode(OBAlignMode.DISABLE)

        self.pipeline.enable_frame_sync()
        self.pipeline.start(config)

    def __del__(self):
        self.pipeline.stop()

    def get_image(self, transnumpy=True):
        depth_data = None
        color_data = None
        pointcloud = None
        while True:
            frames = self.pipeline.wait_for_frames(100)
            if frames is None:
                continue

            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()
            points = frames.get_color_point_cloud(self.pipeline.get_camera_param())

            if color_frame is None or depth_frame is None or points is None:
                continue

            if transnumpy:
                width = depth_frame.get_width()
                height = depth_frame.get_height()
                scale = depth_frame.get_depth_scale()

                # get depth image data
                depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
                depth_data = depth_data.reshape((height, width))
                depth_data = depth_data.astype(np.float32) * scale

                # get rgb image data
                color_data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
                color_data = cv2.imdecode(color_data, cv2.IMREAD_COLOR)
                # color_data = cv2.cvtColor(color_data, cv2.COLOR_RGB2BGR)
                pointcloud = np.array(points[:, :3])
            else:
                depth_data = depth_frame
                color_data = color_frame
                pointcloud= points

            break

        return [depth_data, color_data, pointcloud]
    
    def set_images_save_dir(self, savedir):
        self.savedir = savedir
        self.saveimgdir = os.path.join(savedir,'images')
        self.saveplydir = os.path.join(savedir,'plys')

        if not os.path.exists(self.savedir):
            os.mkdir(self.savedir)
            print("create calibrate dir")

        if not os.path.exists(self.saveimgdir):
            os.mkdir(self.saveimgdir)
            print("create calibrate images dir")

        if not os.path.exists(self.saveplydir):
            os.mkdir(self.saveplydir)
            print("create calibrate plys dir")

        return 0

    def get_images_save_dir(self):
        return self.savedir

    def save_color_and_pointscloud(self, index=None):
        """
        Save aligned color image and high-quality point cloud using PointCloudFilter.
        Uses AlignFilter and PointCloudFilter for professional-grade point cloud generation.
        
        Args:
            index: Optional index to append to the filenames.
        
        Returns:
            0 if successful.
        """
        if self.savedir is None:
            print("Please call 'self.set_images_save_dir()' at first")
            return 0

        # Create align filter and point cloud filter
        align_filter = AlignFilter(align_to_stream=OBStreamType.COLOR_STREAM)
        point_cloud_filter = PointCloudFilter()

        framid = 0 
        while True:
            frames = self.pipeline.wait_for_frames(100)
            if frames is None:
                continue

            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()

            if color_frame is None or depth_frame is None:
                continue
            
            if framid < 10:
                framid += 1
                continue

            # Apply alignment filter to get aligned frames
            try:
                aligned_frames = align_filter.process(frames)
                if aligned_frames is None:
                    continue
                    
                # Get aligned frames for point cloud generation
                aligned_color_frame = aligned_frames.get_color_frame()
                aligned_depth_frame = aligned_frames.get_depth_frame()
                
                if aligned_color_frame is None or aligned_depth_frame is None:
                    continue
                    
            except Exception as e:
                print(f"Error during frame alignment: {e}")
                continue

            # Configure point cloud format based on color frame availability
            has_color_sensor = aligned_color_frame is not None
            point_format = OBFormat.RGB_POINT if has_color_sensor else OBFormat.POINT
            point_cloud_filter.set_create_point_format(point_format)
            
            # Generate point cloud using PointCloudFilter
            try:
                point_cloud_frame = point_cloud_filter.process(aligned_frames)
                if point_cloud_frame is None:
                    print("Failed to generate point cloud")
                    continue
                    
            except Exception as e:
                print(f"Error during point cloud generation: {e}")
                continue

            timestamp = aligned_depth_frame.get_timestamp()

            # Generate filenames
            if index is not None:
                points_filename = os.path.join(self.saveplydir, f"aligned_color_points_{timestamp}_{str(index)}.ply")
                color_filename = os.path.join(self.saveimgdir, f"aligned_color_{timestamp}_{str(index)}.png")
            else:
                points_filename = os.path.join(self.saveplydir, f"aligned_color_points_{timestamp}.ply")
                color_filename = os.path.join(self.saveimgdir, f"aligned_color_{timestamp}.png")

            # Save point cloud using SDK's native function
            try:
                save_point_cloud_to_ply(points_filename, point_cloud_frame)
                print(f"Successfully saved point cloud: {points_filename}")
            except Exception as e:
                print(f"Error saving point cloud: {e}")
                continue

            # Save aligned color image
            try:
                color_data = np.frombuffer(aligned_color_frame.get_data(), dtype=np.uint8)
                color_data = cv2.imdecode(color_data, cv2.IMREAD_COLOR)
                cv2.imwrite(color_filename, color_data)
                print(f"Successfully saved color image: {color_filename}")
            except Exception as e:
                print(f"Error saving color image: {e}")
                continue
                
            break

        return 0

    def save_color_and_depth(self, index=None):
        """
        Save aligned color image and depth image to the specified directory.
        Uses AlignFilter to ensure pixel-perfect alignment between depth and color data.
        
        Args:
            index: Optional index to append to the filenames.
        
        Returns:
            0 if successful.
        """
        if self.savedir is None:
            print("Please call 'self.set_images_save_dir()' at first")
            return 0

        # Create align filter for depth-to-color alignment
        align_filter = AlignFilter(align_to_stream=OBStreamType.COLOR_STREAM)
        
        framid = 0 
        
        while True:
            frames = self.pipeline.wait_for_frames(100)
            if frames is None:
                continue

            # Get original frames
            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()

            if color_frame is None or depth_frame is None:
                continue
            
            if framid < 10:
                framid += 1
                continue

            # Apply alignment filter to get aligned frames
            try:
                aligned_frames = align_filter.process(frames)
                if aligned_frames is None:
                    continue
                    
                # Get aligned depth and color frames
                aligned_depth_frame = aligned_frames.get_depth_frame()
                aligned_color_frame = aligned_frames.get_color_frame()
                
                if aligned_depth_frame is None or aligned_color_frame is None:
                    continue
                    
            except Exception as e:
                print(f"Error during frame alignment: {e}")
                continue

            timestamp = aligned_depth_frame.get_timestamp()
            
            # Process aligned depth data
            width = aligned_depth_frame.get_width()
            height = aligned_depth_frame.get_height()
            scale = aligned_depth_frame.get_depth_scale()
            print(f"-----scale: {scale}-------")
            depth_data = np.frombuffer(aligned_depth_frame.get_data(), dtype=np.uint16)
            depth_data = depth_data.reshape((height, width))

            # Convert to real depth values in meters
            depth_data = depth_data.astype(np.float32) * scale

            # Convert to millimeters and save as uint16 for better file size and precision
            depth_data_to_save = depth_data.astype(np.uint16)  # millimeters

            # Generate filenames with 'aligned' prefix to indicate alignment processing
            if index is not None:
                depth_filename = os.path.join(self.saveimgdir, f"aligned_depth_{timestamp}_{str(index)}.png")
                color_filename = os.path.join(self.saveimgdir, f"aligned_color_{timestamp}_{str(index)}.png")
            else:
                depth_filename = os.path.join(self.saveimgdir, f"aligned_depth_{timestamp}.png")
                color_filename = os.path.join(self.saveimgdir, f"aligned_color_{timestamp}.png")

            # Save aligned depth image as 16-bit PNG for better precision
            cv2.imwrite(depth_filename, depth_data_to_save)
            
            # Save aligned color image
            color_data = np.frombuffer(aligned_color_frame.get_data(), dtype=np.uint8)
            color_data = cv2.imdecode(color_data, cv2.IMREAD_COLOR)
            cv2.imwrite(color_filename, color_data)
            
            print(f"Successfully saved aligned images: {depth_filename}, {color_filename}")
            break

        return 0
        
    def _save_camera_params(self, camera_param, filename, depth_scale):
        """
        Save camera parameters to a JSON file for later use in point cloud generation.
        
        Args:
            camera_param: Camera parameters from the pipeline
            filename: File path to save parameters
            depth_scale: Depth scale factor
        """
        import json
        
        # Extract intrinsic parameters
        rgb_intrinsic = {
            "width": camera_param.rgb_intrinsic.width,
            "height": camera_param.rgb_intrinsic.height,
            "fx": camera_param.rgb_intrinsic.fx,
            "fy": camera_param.rgb_intrinsic.fy,
            "cx": camera_param.rgb_intrinsic.cx,
            "cy": camera_param.rgb_intrinsic.cy
        }
        
        depth_intrinsic = {
            "width": camera_param.depth_intrinsic.width,
            "height": camera_param.depth_intrinsic.height,
            "fx": camera_param.depth_intrinsic.fx,
            "fy": camera_param.depth_intrinsic.fy,
            "cx": camera_param.depth_intrinsic.cx,
            "cy": camera_param.depth_intrinsic.cy
        }
        
        # Extract distortion parameters
        rgb_distortion = {
            "k1": camera_param.rgb_distortion.k1,
            "k2": camera_param.rgb_distortion.k2,
            "k3": camera_param.rgb_distortion.k3,
            "k4": camera_param.rgb_distortion.k4,
            "k5": camera_param.rgb_distortion.k5,
            "k6": camera_param.rgb_distortion.k6,
            "p1": camera_param.rgb_distortion.p1,
            "p2": camera_param.rgb_distortion.p2
        }
        
        depth_distortion = {
            "k1": camera_param.depth_distortion.k1,
            "k2": camera_param.depth_distortion.k2,
            "k3": camera_param.depth_distortion.k3,
            "k4": camera_param.depth_distortion.k4,
            "k5": camera_param.depth_distortion.k5,
            "k6": camera_param.depth_distortion.k6,
            "p1": camera_param.depth_distortion.p1,
            "p2": camera_param.depth_distortion.p2
        }
        
        # Extract extrinsic parameters (transform matrix)
        # The transform contains rotation and translation from depth to color
        transform_rotation = list(camera_param.transform.rot.to_list())
        transform_translation = list(camera_param.transform.trans.to_list())
        
        # Create 4x4 transformation matrix
        extrinsic_matrix = [
            [transform_rotation[0][0], transform_rotation[0][1], transform_rotation[0][2], transform_translation[0]],
            [transform_rotation[1][0], transform_rotation[1][1], transform_rotation[1][2], transform_translation[1]],
            [transform_rotation[2][0], transform_rotation[2][1], transform_rotation[2][2], transform_translation[2]],
            [0, 0, 0, 1]
        ]
            
        # Create parameter dictionary
        params = {
            "rgb_intrinsic": rgb_intrinsic,
            "depth_intrinsic": depth_intrinsic,
            "rgb_distortion": rgb_distortion,
            "depth_distortion": depth_distortion,
            "depth2rgb_extrinsic": extrinsic_matrix,
            "depth_scale": depth_scale
        }
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(params, f, indent=4)
        
        return
        
    def save_camera_params(self, filename=None, index=None):
        """
        Save camera parameters to a JSON file for later use in point cloud generation.
        This is a standalone function that can be called independently.
        
        Args:
            filename: Optional file path to save parameters. If None, a default name will be used.
            index: Optional index to append to the filename.
            
        Returns:
            The path to the saved parameter file or None if failed.
        """
        if self.savedir is None and filename is None:
            print("Please either call 'self.set_images_save_dir()' first or provide a filename")
            return None
            
        # Get camera parameters
        camera_param = self.pipeline.get_camera_param()
        if camera_param is None:
            print("Failed to get camera parameters")
            return None
            
        # Get depth scale - need to get a depth frame first
        depth_scale = 1.0  # Default value
        frames = self.pipeline.wait_for_frames(100)
        if frames is not None:
            depth_frame = frames.get_depth_frame()
            if depth_frame is not None:
                depth_scale = depth_frame.get_depth_scale()
                
        # Generate filename if not provided
        if filename is None:
            timestamp = int(time.time() * 1000)  # Current time in milliseconds
            if index is not None:
                param_filename = os.path.join(self.saveimgdir, f"camera_param_{timestamp}_{str(index)}.json")
            else:
                param_filename = os.path.join(self.saveimgdir, f"camera_param_{timestamp}.json")
        else:
            param_filename = filename
            
        # Save parameters
        self._save_camera_params(camera_param, param_filename, depth_scale)
        
        return param_filename

    def visualize(self, savedir=None):
        
        try:
            while True:
                try:
                    [depth_data,color_data,_] = self.get_image(transnumpy=True)

                    if depth_data is None or color_data is None:
                        continue

                    # use pseudo-color mapper to convert depth data to pseudo-color image
                    depth_colormap = cv2.normalize(depth_data, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
                    depth_colormap = cv2.applyColorMap(depth_colormap, cv2.COLORMAP_JET)
                    # create a window to display color image and pseudo-color depth image
                    # combined_image = np.hstack((color_data, depth_colormap))
                    # cv2.imshow("Combined Image", combined_image)

                    align_image = cv2.addWeighted(color_data, 0.5, depth_colormap, 0.5, 0)
                    cv2.imshow("SyncAlignViewer ", align_image)
                    # press Esc key to exit loop
                    key = cv2.waitKey(1)
                    if key == 27:
                        break
                except RuntimeError as e:
                    print(f"error when waiting for frame: {e}")

        finally:
            cv2.destroyAllWindows()

    def save_image(self, imgdir):
        pass


if __name__=="__main__":
    orb_camera = OrbCamera()

    # orb_camera.visualize()

    # [depthdata,colordata] = orb_camera.get_image()

    # print(['depthdata shape:',depthdata.shape])
    # print(['colordata shape:',colordata.shape])

    orb_camera.set_images_save_dir('../../output/datacollect2')

    collecttime = 100
    while collecttime:
        orb_camera.save_color_and_pointscloud()
        collecttime-=1
        time.sleep(1)

        print('change image!!!!!!!!!!!!!!!')
