from pyorbbecsdk import Pipeline, Config, OBSensorType, OBError, AlignFilter, OBStreamType, OBAlignMode, FrameSet,OBCameraParam
import os
import numpy as np
import cv2  # 添加OpenCV用于图像保存

# 创建保存图像的目录
save_images_dir = os.path.join(os.getcwd(), "aligned_images")
if not os.path.exists(save_images_dir):
    os.makedirs(save_images_dir)

def save_depth_image(depth_frame, filename):
    """Save depth frame as 16-bit PNG file"""
    if depth_frame is None:
        return
    # 将深度帧转换为numpy数组
    depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
    depth_data = depth_data.reshape((depth_frame.get_height(), depth_frame.get_width()))
    # 保存为16位PNG（保留原始深度值）
    cv2.imwrite(filename, depth_data)
    print(['depthdata shape:',depth_data.shape])
    

def save_color_image(color_frame, filename):
    """Save color frame as RGB image file"""
    if color_frame is None:
        return
    # 将彩色帧转换为RGB numpy数组
    color_data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
    color_data = cv2.imdecode(color_data, cv2.IMREAD_COLOR)

    cv2.imwrite(filename, color_data)
    print(['colordata shape:',color_data.shape])

def convert_depth_frame_to_numpy(depth_frame):
    """Convert depth frame to numpy array"""
    if depth_frame is None:
        return None
    depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
    depth_data = depth_data.reshape((depth_frame.get_height(), depth_frame.get_width()))
    return depth_data

def convert_color_frame_to_numpy(color_frame):
    """Convert color frame to numpy array (BGR format)"""
    if color_frame is None:
        return None
    color_data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
    color_data = cv2.imdecode(color_data, cv2.IMREAD_COLOR)
    return color_data

def capture_images(save_images=False):
    """
    Capture aligned color and depth images from Orbbec camera
    
    Args:
        save_images (bool): Whether to save images to disk
        
    Returns:
        tuple: (color_image, depth_image, camera_param) where:
            - color_image: numpy array of color image (BGR format)
            - depth_image: numpy array of depth image (uint16, millimeters)
            - camera_param: camera parameters object
            Returns (None, None, None) if capture fails
    """
    pipeline = Pipeline()
    config = Config()
    
    try:
        # 配置深度流
        depth_profile_list = pipeline.get_stream_profile_list(OBSensorType.DEPTH_SENSOR)
        if depth_profile_list is None:
            print("未找到深度配置，无法继续")
            return None, None, None
        depth_profile = depth_profile_list.get_default_video_stream_profile()
        config.enable_stream(depth_profile)

        has_color_sensor = False
        try:
            # 配置彩色流（如果可用）
            profile_list = pipeline.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
            if profile_list is not None:
                color_profile = profile_list.get_default_video_stream_profile()
                config.enable_stream(color_profile)
                has_color_sensor = True
        except OBError as e:
            print(f"Color sensor configuration error: {e}")

        pipeline.enable_frame_sync()
        pipeline.start(config)
        
        # 创建对齐过滤器（深度对齐到彩色）
        align_filter = AlignFilter(align_to_stream=OBStreamType.COLOR_STREAM)

        # Capture loop
        max_attempts = 100  # Maximum attempts to get valid frames
        for attempt in range(max_attempts):
            frames = pipeline.wait_for_frames(100)
            if frames is None:
                continue
            
            # 原始深度帧（未对齐）
            depth_frame = frames.get_depth_frame()
            if depth_frame is None:
                continue
            
            # 原始彩色帧
            color_frame = frames.get_color_frame()
            if has_color_sensor and color_frame is None:
                continue
            
            # 对齐帧（深度图对齐到彩色图）
            aligned_frames = align_filter.process(frames)
            if aligned_frames is None:
                continue
                
            # 从对齐后的帧中获取深度帧和彩色帧
            aligned_depth_frame = aligned_frames.get_depth_frame()
            aligned_color_frame = aligned_frames.get_color_frame()
            
            if aligned_depth_frame is None or aligned_color_frame is None:
                continue
            
            # Convert frames to numpy arrays
            depth_image = convert_depth_frame_to_numpy(aligned_depth_frame)
            color_image = convert_color_frame_to_numpy(aligned_color_frame)
            
            if depth_image is None or color_image is None:
                continue
            
            # Get camera parameters
            camera_param = pipeline.get_camera_param()
            
            # Optionally save images to disk
            if save_images:
                save_depth_image(aligned_depth_frame, os.path.join(save_images_dir, "aligned_depth.png"))
                save_color_image(aligned_color_frame, os.path.join(save_images_dir, "color.png"))
                print("成功保存对齐图像！")
            
            print(f"成功采集图像！Color shape: {color_image.shape}, Depth shape: {depth_image.shape}")
            print(f"camera_param: {camera_param}")
            
            return color_image, depth_image, camera_param
            
        print(f"Failed to capture valid frames after {max_attempts} attempts")
        return None, None, None
        
    except Exception as e:
        print(f"Error during image capture: {e}")
        return None, None, None
    finally:
        try:
            pipeline.stop()
            print("停止采集")
        except:
            pass

def main():
    """Main function for standalone execution - captures and saves images"""
    color_image, depth_image, camera_param = capture_images(save_images=True)
    
    if color_image is not None and depth_image is not None:
        print("图像采集成功完成！")
    else:
        print("图像采集失败！")
    print(f"camera_param: {camera_param}")

if __name__ == "__main__":
    main()