import os
import pyrealsense2 as rs
import numpy as np
import cv2
import time
import datetime

def record_bag():
    """
    Record a .bag file from the RealSense camera, saving both color and depth streams, and display them in real time.
    """
    # Create output directory
    output_dir = os.path.join(os.getcwd(), 'bag_files')
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Use current time as filename
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    bag_filename = os.path.join(output_dir, f'record_{timestamp}.bag')
    
    # Initialize camera config
    pipeline = rs.pipeline()
    config = rs.config()
    
    # Configure streams - enable color and depth, 640x480@30fps
    config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
    config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
    
    # Set to record to bag file
    config.enable_record_to_file(bag_filename)
    
    # Start streaming
    print(f"Start recording to file: {bag_filename}")
    pipeline.start(config)
    
    try:
        # Track time and frame count
        start_time = time.time()
        frame_count = 0
        
        while True:
            # Wait for frames
            frames = pipeline.wait_for_frames()
            
            # Get color and depth frames
            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()
            
            if not color_frame or not depth_frame:
                continue
            
            # Convert to numpy arrays for display
            color_image = np.asanyarray(color_frame.get_data())
            # Apply color map to depth image
            depth_colormap = np.asanyarray(
                rs.colorizer().colorize(depth_frame).get_data())
            
            # Calculate elapsed time and FPS
            frame_count += 1
            elapsed_time = time.time() - start_time
            fps = frame_count / elapsed_time if elapsed_time > 0 else 0
            
            # Overlay time info on image
            minutes = int(elapsed_time / 60)
            seconds = int(elapsed_time % 60)
            text = f"Recording: {minutes:02d}:{seconds:02d} | FPS: {fps:.1f}"
            cv2.putText(color_image, text, (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Show images
            cv2.imshow('RealSense Recording', color_image)
            cv2.imshow('Depth View', depth_colormap)
            
            # Press ESC to exit
            key = cv2.waitKey(1)
            if key == 27:  # ESC
                print(f"Recording finished, total frames: {frame_count}, total time: {elapsed_time:.2f} seconds")
                break
    finally:
        # Stop streaming and clean up
        pipeline.stop()
        cv2.destroyAllWindows()
        print(f"Recorded file saved to: {bag_filename}")

def basetest1(color_save_dir, depth_save_dir):
    """
    Capture and save aligned depth and color images from a RealSense camera.
    Args:
        color_save_dir (str): Directory to save color images.
        depth_save_dir (str): Directory to save depth images.
    """
    # Initialize depth camera
    pipeline = rs.pipeline()
    config = rs.config()
    config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
    config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
    pipeline.start(config)
    try:
        while True:
            try:
                print("Waiting for camera frames...")
                # Wait for aligned frames so both depth and color frames can be obtained simultaneously
                align_to = rs.stream.color
                align = rs.align(align_to)

                # Wait for a few frames to allow auto-exposure and other adjustments to stabilize
                for _ in range(30):
                    frames = pipeline.wait_for_frames(timeout_ms=15000)
                    # Align frames
                    aligned_frames = align.process(frames)

                # Get aligned depth and color frames
                depth_frame = aligned_frames.get_depth_frame()
                color_frame = aligned_frames.get_color_frame()

                if not depth_frame or not color_frame:
                    print("Failed to get depth or color frame. Please check camera connection and stream configuration.")
                    return # Exit the function if frames are not acquired

                print("Camera frames received.")

                # Get raw data from depth image
                depth_data = np.asanyarray(depth_frame.get_data())
                # Get raw data from RGB image
                color_data = np.asanyarray(color_frame.get_data())

                t = time.time()
                tname = str(t)[5:10]  # Extract seconds from timestamp as image name

                # Save images to specified folders, using different filenames
                color_image_name = 'color_image_name' + str(tname) + '.png'
                depth_image_name = 'depth_image_name' + str(tname) + '.tif'

                cv2.imwrite(os.path.join(color_save_dir, color_image_name), color_data)
                cv2.imwrite(os.path.join(depth_save_dir, depth_image_name), depth_data)

                # visulaztion
                depth_colormap = np.asanyarray(
                        rs.colorizer().colorize(depth_frame).get_data())
                cv2.imshow("Color Image", color_data)
                cv2.imshow('Depth View', depth_colormap)

                # Press Esc to exit the loop
                key = cv2.waitKey(1)
                if key == 27:
                    break
            except RuntimeError as e:
                print(f"Error occurred while waiting for frames: {e}")
    finally:
        pipeline.stop()
        cv2.destroyAllWindows()

def get_camera_intrinsics():
    """
    Get the intrinsic parameters of the RealSense camera using the pyrealsense2 library.
    """
    # Create a pipeline object
    pipeline = rs.pipeline()
    # Create a config object
    config = rs.config()

    # Configure the streams to enable. Here we enable depth and color streams.
    # You can modify the resolution and frame rate as needed.
    config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
    config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)

    try:
        # Start the stream
        print("Starting RealSense camera stream...")
        pipeline.start(config)
        print("Camera stream started.")

        # Wait for a frame to get stream configuration
        frames = pipeline.wait_for_frames()

        # Get the configuration of depth and color streams
        depth_profile = frames.get_depth_frame().get_profile()
        color_profile = frames.get_color_frame().get_profile()

        # Convert stream profiles to video stream profiles to get intrinsics
        depth_video_profile = depth_profile.as_video_stream_profile()
        color_video_profile = color_profile.as_video_stream_profile()

        # Get intrinsics for depth and color streams
        depth_intrinsics = depth_video_profile.get_intrinsics()
        color_intrinsics = color_video_profile.get_intrinsics()

        print("\n--- Depth Camera Intrinsics ---")
        print(f"Width: {depth_intrinsics.width}")
        print(f"Height: {depth_intrinsics.height}")
        print(f"Focal length fx: {depth_intrinsics.fx}")
        print(f"Focal length fy: {depth_intrinsics.fy}")
        print(f"Principal point cx: {depth_intrinsics.ppx}")
        print(f"Principal point cy: {depth_intrinsics.ppy}")
        print(f"Distortion model: {depth_intrinsics.model}")
        print(f"Distortion coefficients: {depth_intrinsics.coeffs}")

        print("\n--- Color Camera Intrinsics ---")
        print(f"Width: {color_intrinsics.width}")
        print(f"Height: {color_intrinsics.height}")
        print(f"Focal length fx: {color_intrinsics.fx}")
        print(f"Focal length fy: {color_intrinsics.fy}")
        print(f"Principal point cx: {color_intrinsics.ppx}")
        print(f"Principal point cy: {color_intrinsics.ppy}")
        print(f"Distortion model: {color_intrinsics.model}")
        print(f"Distortion coefficients: {color_intrinsics.coeffs}")

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        # Stop the stream
        print("\nStopping camera stream...")
        pipeline.stop()
        print("Camera stream stopped.")

if __name__ == "__main__":
    # Example usage:
    color_save_dir = "./color_images"
    depth_save_dir = "./depth_images"
    os.makedirs(color_save_dir, exist_ok=True)
    os.makedirs(depth_save_dir, exist_ok=True)
    # Uncomment the function want to run:
    # basetest1(color_save_dir, depth_save_dir)
    # get_camera_intrinsics()
