import cv2
import sys
import torch
import open3d as o3d
import numpy as np
import PIL.Image
import matplotlib.pyplot as plt

from utils import SAMTRTPredictor, YOLOv8TRTDetector, mask_to_point_cloud
from ransac_icp import register_and_transform_grasp_points
from scipy.spatial.transform import Rotation as R_scipy


def bbox2points(bbox):
    """Convert bounding box to points for SAM"""
    points = np.array([
        [bbox[0], bbox[1]],  # top-left
        [bbox[2], bbox[3]]   # bottom-right
    ])

    point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
    return points, point_labels

def draw_bbox(bbox, color='g'):
    """Draw bounding box on the plot"""
    x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
    y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
    plt.plot(x, y, color + '-')

def subplot_notick(a, b, c):
    """Create subplot without ticks"""
    ax = plt.subplot(a, b, c)
    ax.set_xticklabels([])
    ax.set_yticklabels([])
    ax.axis('off')


def main():
    # Path to files
    color_path = "data/img/2.png"
    depth_path = "data/img/2.tif"
    obj_path = "data/mesh/cad.ply"
    
    # config infer mask
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"device: {device}")
    yolo_engine = 'weights/yolov8s_basket.engine'
    image_encoder = 'weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'weights/mobile_sam_mask_decoder_fp16.engine'

    # Initialize pose estimator
    print("Initialize pose estimator")
    detector = YOLOv8TRTDetector(yolo_engine, device)
    sam_predictor = SAMTRTPredictor(device, image_encoder, mask_decoder)
    
    # yolo infer
    pil_image = PIL.Image.open(color_path).convert('RGB')
    cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    detections = detector.predict(cv_image)
   
    N = len(detections['bboxes'])
    
    if N == 0:
        print("No objects detected with the current settings.")
        sys.exit(0)

    bbox = detections['bboxes'][0]

    # sam infer
    points, point_labels = bbox2points(bbox)
    sam_predictor.set_image(pil_image)
    mask, _, _ = sam_predictor.predict(points, point_labels)
    mask = (mask[0, 0] > 0).detach().cpu().numpy()
    # mask = mask * 255
    mask = mask.astype(np.uint8) * 255

    if True:
        # pil_mask = PIL.Image.fromarray(mask, 'L')
        # print(f"pil_mask.size: {pil_mask.size}")
        # pil_mask.save("mask.png")

        subplot_notick(2, N, 1)
        plt.imshow(pil_image)
        draw_bbox(bbox)
        plt.title("Detection")

        subplot_notick(2, N, 2)
        plt.imshow(pil_image)
        plt.imshow(mask, alpha=0.5)
        plt.title("Segmentation Mask")
        plt.subplots_adjust(wspace=0.05, hspace=0.2)
        plt.savefig('out.png', bbox_inches="tight")

       # plt.show()
       
    # estimate pose
    rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
    depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
    depth = depth.astype(np.float32)    
    depth = depth / 1000.0   # scale to meter, mm -> m

    # generate point cloud from rgb and depth dependant on mask
    intrinsic = np.array([
        [647.4208984375, 0, 643.5106201171875],
        [0, 646.6436157226562, 360.1596374511719],
        [0, 0, 1]   
    ]) # color intrinsic
    # mask to point cloud
    camera_pcd = mask_to_point_cloud(rgb, depth, mask, intrinsic)
    cad_pcd = o3d.io.read_point_cloud(obj_path)

    # --- Run Calculation ---
    grasp_points_on_cad = np.array([
        [-0.122480, 0.068206, -0.142390],
        [0.122098, -0.155091, 0.061945]
    ])

    grasp_rotation_on_cad = np.array([
        [0.61901091,  0.77083935, -0.15044  ],
        [-0.52696196, 0.54967489,  0.64820414],
        [ 0.58235435, -0.32196928, 0.74645776]
    ])

    T = np.array([
        [1, 0, 0, 0],
        [0, 1, 0, 0],
        [0, 0, 1, 0],
        [0, 0, 0, 1]
    ]) # camera -> world

    # CAD -> camera
    grasp_points_in_camera, grasp_rotation_in_camera, transformation = register_and_transform_grasp_points(
        cad_pcd, camera_pcd, grasp_rotation_on_cad, grasp_points_on_cad, voxel_size=0.004
    )

    # 1. conversion from camera to world
    R_world_from_cam = T[:3, :3]  # T matrix rotation part
    # t_world_from_cam = T[:3, 3]  # T matrix translation part

    # 2. Transform grasp points
    # Convert grasp points to homogeneous coordinates (N, 4)
    grasp_points_in_camera_homogeneous = np.hstack(
        (grasp_points_in_camera, np.ones((grasp_points_in_camera.shape[0], 1)))
    )
    # Apply transformation: T (4x4) @ points_homogeneous.T (4xN) -> (4xN), then .T -> (Nx4)
    grasp_points_in_world_homogeneous = (T @ grasp_points_in_camera_homogeneous.T).T
    # Convert back to 3D coordinates (N, 3)
    grasp_points_in_world = grasp_points_in_world_homogeneous[:, :3]

    # 3. Transform grasp rotation
    grasp_rotation_in_world = R_world_from_cam @ grasp_rotation_in_camera

    # 4. Convert world rotation matrix to Euler angles (Rx, Ry, Rz)
    # Use 'xyz' order (representing Rx rotation about the new X axis, then Ry rotation about the new Y axis, finally Rz rotation about the new Z axis)
    scipy_rotation_world = R_scipy.from_matrix(grasp_rotation_in_world)
    grasp_euler_angles_in_world_deg = scipy_rotation_world.as_euler('xyz', degrees=True)

    print('\n--- World Coordinate Transformations ---')
    print('Grasp points in world coordinate:')
    print(grasp_points_in_world)
    print('\nGrasp rotation matrix in world coordinate:')
    print(grasp_rotation_in_world)
    print('\nGrasp Euler angles (Rx, Ry, Rz) in world coordinate (degrees, XYZ intrinsic):')
    print(grasp_euler_angles_in_world_deg)

    # 5. Visualize registration result (CAD model aligned to camera point cloud)
    print("\nVisualizing registration result (CAD model aligned to camera point cloud)...")
    print("Please close the Open3D window to continue the script.")

    # Create point cloud  
    cad_pcd_vis = o3d.geometry.PointCloud()
    cad_pcd_vis.points = o3d.utility.Vector3dVector(np.asarray(cad_pcd.points))

    camera_pcd_vis = o3d.geometry.PointCloud()
    camera_pcd_vis.points = o3d.utility.Vector3dVector(np.asarray(camera_pcd.points))

    cad_pcd_transformed_vis = cad_pcd_vis.transform(transformation)

    # Set colors for distinction
    cad_pcd_transformed_vis.paint_uniform_color([1.0, 0.706, 0.0])  # Orange: registered CAD model
    camera_pcd_vis.paint_uniform_color([0.0, 0.651, 0.929])   # Blue: camera point cloud
    
    # Optional: Display original CAD model for reference
    # cad_pcd_original_vis = o3d.geometry.PointCloud()
    # cad_pcd_original_vis.points = o3d.utility.Vector3dVector(np.asarray(cad_pcd.points))
    # cad_pcd_original_vis.paint_uniform_color([0.5, 0.5, 0.5]) # Gray: original CAD model
    # o3d.visualization.draw_geometries([cad_pcd_transformed_vis, camera_pcd_vis, cad_pcd_original_vis],
    #                                   window_name="Registration: Transformed CAD (Orange), Camera PCD (Blue), Original CAD (Gray)",
    #                                   width=800, height=600)

    o3d.visualization.draw_geometries([cad_pcd_transformed_vis, camera_pcd_vis],
                                      window_name="Registration: Transformed CAD (Orange) vs Camera PCD (Blue)",
                                      width=800, height=600)

    # save point cloud
   # o3d.io.write_point_cloud("cad_pcd_transformed_vis.ply", cad_pcd_transformed_vis)
   # o3d.io.write_point_cloud("camera_pcd_vis.ply", camera_pcd_vis)

    # save merged point cloud
    merged_vis = cad_pcd_transformed_vis + camera_pcd_vis
    o3d.io.write_point_cloud("registration_vis_merged.ply", merged_vis)

if __name__ == "__main__":
    main()
