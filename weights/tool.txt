trtexec \
    --onnx=mobile_sam_mask_decoder.onnx \
    --saveEngine=mobile_sam_mask_decoder_fp32.engine \
    --minShapes=point_coords:1x1x2,point_labels:1x1 \
    --optShapes=point_coords:1x1x2,point_labels:1x1 \
    --maxShapes=point_coords:1x10x2,point_labels:1x10 \
   	

trtexec \
    --onnx=resnet18_image_encoder.onnx \
    --saveEngine=resnet18_image_encoder.engine \
    --fp16


trtexec \
	--onnx=yolov8s_blue_v3.onnx \
	--saveEngine=yolov8s_blue_v3.engine \
	--fp16
	

trtexec \
	--onnx=mobile_sam_image_encoder.onnx \
	--saveEngine=mobile_sam_image_encoder.engine \
	--fp16
