import cv2
import torch
import numpy as np
import PIL.Image
import matplotlib.pyplot as plt
import time
import sys
from a2d_sdk.robot import CosineCamera as Camera
from a2d_sdk.robot import RobotDds as Robot

# Import grasp estimation modules
from utils import SAMTRTPredictor, YOLOv8TRTDetector, mask_to_point_cloud
from find_grasp_obb_align import calculate_gripping_pose_obb

def draw_bbox(bbox, color='g'):
    """Draw bounding box on the plot"""
    x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
    y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
    plt.plot(x, y, color + '-')

def subplot_notick(a, b, c):
    """Create subplot without ticks"""
    ax = plt.subplot(a, b, c)
    ax.set_xticklabels([])
    ax.set_yticklabels([])
    ax.axis('off')

class SDKGraspWrapper:
    def __init__(self, 
                 yolo_engine='weights/yolov8s_basket.engine',
                 image_encoder='weights/resnet18_image_encoder_fp16.engine',
                 mask_decoder='weights/mobile_sam_mask_decoder_fp16.engine',
                 frame_skip=3,
                 visualize=True,
                 rim_threshold=0.20,
                 save_results=True,
                 output_dir='out_grasp'):
        """
        Initialize SDK Grasp Wrapper
        
        Args:
            yolo_engine: Path to YOLOv8 TensorRT engine
            image_encoder: Path to SAM image encoder TensorRT engine
            mask_decoder: Path to SAM mask decoder TensorRT engine
            frame_skip: Process one frame for every N frames (default: 3)
            visualize: Whether to visualize results (default: True)
            rim_threshold: Threshold for rim height detection (default: 0.20)
            save_results: Whether to save results (default: True)
            output_dir: Directory to save results (default: 'out_grasp')
        """
        self.frame_skip = frame_skip
        self.visualize = visualize
        self.rim_threshold = rim_threshold
        self.save_results = save_results
        self.output_dir = output_dir
        self.frame_count = 0
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Initialize robot and cameras
        print("Initializing robot and cameras...")
        self.robot = Robot()
        self.cameras_head = Camera(["head"])
        self.cameras_depth = Camera(["head_depth"])
        print("Initialization complete!")
        
        # Wait for cameras to initialize
        time.sleep(3)
        
        # Define camera intrinsic parameters
        self.intrinsic = np.array([
            [647.4208984375, 0, 643.5106201171875],
            [0, 646.6436157226562, 360.1596374511719],
            [0, 0, 1]   
        ]) # color intrinsic
        
        # Initialize pose estimation models
        print(f"Initializing models on {self.device}...")
        self.detector = YOLOv8TRTDetector(yolo_engine, self.device)
        self.sam_predictor = SAMTRTPredictor(self.device, image_encoder, mask_decoder)
        print("Model initialization complete!")
    
    def bbox2points(self, bbox):
        """Convert bounding box to points for SAM"""
        points = np.array([
            [bbox[0], bbox[1]],  # top-left
            [bbox[2], bbox[3]]   # bottom-right
        ])
        point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
        return points, point_labels
    
    def draw_bbox(self, img, bbox, color=(0, 255, 0), thickness=2):
        """Draw bounding box on the image"""
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(img, (x1, y1), (x2, y2), color, thickness)
        return img
    
    def draw_mask(self, img, mask, color=(0, 0, 255), alpha=0.5):
        """Draw mask overlay on the image"""
        colored_mask = np.zeros_like(img)
        colored_mask[:,:,0] = color[0]
        colored_mask[:,:,1] = color[1]
        colored_mask[:,:,2] = color[2]
        
        mask_image = np.zeros_like(img)
        mask_bool = mask.astype(bool)
        mask_image[mask_bool] = colored_mask[mask_bool]
        
        return cv2.addWeighted(img, 1, mask_image, alpha, 0)
    
    def get_camera_images(self):
        """Get latest color and depth images from cameras"""
        # Get current robot state (for reference)
        # arm_pos, timestamp = self.robot.arm_joint_states()
        # head_pos, timestamp = self.robot.head_joint_states()
        
        # Get latest color image
        color_img_rgb, time_stamp_color = self.cameras_head.get_latest_image("head")
        if color_img_rgb is None:
            return None, None
        
        # Get latest depth image
        depth_img, time_stamp_depth = self.cameras_depth.get_latest_image("head_depth")
        if depth_img is None:
            return color_img_rgb, None
            
        # Convert depth to meters
        depth_img = depth_img.astype(np.float32) / 1000.0  # mm to meters
        
        return color_img_rgb, depth_img
    
    def estimate_grasp(self, color_img_rgb, depth_img):
        """Estimate grasp pose from color and depth images"""
        # Convert to BGR for YOLO detection
        cv_image = cv2.cvtColor(color_img_rgb, cv2.COLOR_RGB2BGR)
        
        # Object detection with YOLO
        detections = self.detector.predict(cv_image)
        bboxes = detections['bboxes']
        
        if len(bboxes) == 0:
            print("No objects detected")
            return None, None, None, None
        
        # Get the first bbox (could be enhanced to select best object)
        bbox = bboxes[0]
        
        # Convert RGB image to PIL for SAM
        pil_image = PIL.Image.fromarray(color_img_rgb)
        
        # Segmentation with SAM
        points, point_labels = self.bbox2points(bbox)
        self.sam_predictor.set_image(pil_image)
        mask, _, _ = self.sam_predictor.predict(points, point_labels)
        mask = (mask[0, 0] > 0).detach().cpu().numpy()
        mask = mask.astype(np.uint8) * 255
        
        if True:
            pil_mask = PIL.Image.fromarray(mask, 'L')
            print(f"pil_mask.size: {pil_mask.size}")
            pil_mask.save("mask.png")

            subplot_notick(2, 1, 1)
            plt.imshow(pil_image)
            draw_bbox(bbox)
            plt.title("Detection")

            subplot_notick(2, 1, 2)
            plt.imshow(pil_image)
            plt.imshow(mask, alpha=0.5)
            plt.title("Segmentation Mask")
            plt.subplots_adjust(wspace=0.05, hspace=0.2)
            plt.savefig('out.png', bbox_inches="tight")

        # Generate point cloud from RGB and depth using mask
        object_pcd = mask_to_point_cloud(color_img_rgb, depth_img, mask, self.intrinsic)
        
        # Calculate gripping pose
        grip_pos, grip_rot, edge_pts = calculate_gripping_pose_obb(
            object_pcd,
            rim_height_threshold_factor=self.rim_threshold,
            visualize=self.visualize,  # Use the visualization parameter from class initialization
            output_path=f"{self.output_dir}/out_{self.frame_count}" if self.save_results else None
        )
        
        return bbox, mask, grip_pos, grip_rot, edge_pts
    
    def run(self):
        """Run the main loop"""
        try:
            print("Starting real-time grasp estimation...")
            
            while True:
                start_time = time.time()
                
                # Get images from cameras
                color_img, depth_img = self.get_camera_images()
                if color_img is None:
                    print("Failed to get color image, retrying...")
                    time.sleep(0.1)
                    continue
                    
                if depth_img is None:
                    print("Failed to get depth image, retrying...")
                    time.sleep(0.1)
                    continue
                
                # Increment frame counter
                self.frame_count += 1
                
                # Display the raw image
                display_img = color_img.copy()
                display_img = cv2.cvtColor(display_img, cv2.COLOR_RGB2BGR)  # Convert back to BGR for OpenCV display
                
                # Process every Nth frame (frame skipping)
                if self.frame_count % self.frame_skip == 0:
                    print(f"Processing frame {self.frame_count}")
                    
                    # Estimate grasp pose
                    result = self.estimate_grasp(color_img, depth_img)
                    
                    if result is not None:
                        bbox, mask, grip_pos, grip_rot, edge_pts = result
                        
                        # Visualize detection and segmentation
                        if bbox is not None:
                            display_img = self.draw_bbox(display_img, bbox)
                            
                        if mask is not None:
                            display_img = self.draw_mask(display_img, mask)
                        
                        # Display grasp results if available
                        if edge_pts is not None and grip_rot is not None:
                            print("\n--- Grasp Results ---")
                            print(f"Gripping Point Position: {edge_pts}")
                            print(f"Gripping Orientation:")
                            print(grip_rot)
                            
                            pt1, pt2 = edge_pts
                            info_text = f"Edge pts: [{pt1[0]:.2f}, {pt1[1]:.2f}, {pt1[2]:.2f}], [{pt2[0]:.2f}, {pt2[1]:.2f}, {pt2[2]:.2f}]"
                            # Add text overlay for edge points
                            cv2.putText(display_img, info_text, (10, 60), 
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                            # # Add text overlay for grasp position
                            # cv2.putText(display_img, f"Grip: {grip_pos[0]:.3f}, {grip_pos[1]:.3f}, {grip_pos[2]:.3f}", 
                            #           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    else:
                        print("Failed to estimate grasp")
                
                # Display fps counter
                fps = 1.0 / (time.time() - start_time)
                cv2.putText(display_img, f"FPS: {fps:.1f}", (10, display_img.shape[0] - 10),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                
                # Display the image
                cv2.imshow("SDK Grasp Wrapper", display_img)
                
                # Check for exit key (Esc)
                key = cv2.waitKey(1)
                if key == 27:  # Esc key
                    break
                    
        except KeyboardInterrupt:
            print("User interrupted")
        except Exception as e:
            print(f"Error: {e}")
        finally:
            # Cleanup
            self.cameras_head.close()
            self.cameras_depth.close()
            cv2.destroyAllWindows()
            print("Cameras closed, exiting")


def main():
    """Main function"""
    # Default parameters
    yolo_engine = 'weights/yolov8s_basket.engine'
    image_encoder = 'weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'weights/mobile_sam_mask_decoder_fp16.engine'
    
    # Create and run the SDK Grasp Wrapper
    grasp_wrapper = SDKGraspWrapper(
        yolo_engine=yolo_engine,
        image_encoder=image_encoder,
        mask_decoder=mask_decoder,
        rim_threshold=0.20,
        frame_skip=3,  # Process every 3rd frame
        visualize=True,
        save_results=True,
        output_dir='out_grasp'
    )
    
    grasp_wrapper.run()


if __name__ == "__main__":
    main()
