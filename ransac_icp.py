import open3d as o3d
import numpy as np

def register_point_clouds(source_pcd: o3d.geometry.PointCloud, target_pcd: o3d.geometry.PointCloud, voxel_size=0.02):
    """
    Perform coarse registration using RANSAC and fine registration using ICP, and return the 4x4 transformation matrix.
    Args:
        source_pcd: open3d.geometry.PointCloud, the source point cloud
        target_pcd: open3d.geometry.PointCloud, the target point cloud
        voxel_size: float, voxel size for downsampling
    Returns:
        transformation: 4x4 numpy array, the final transformation matrix
    """
    # Downsample
    source_down = source_pcd.voxel_down_sample(voxel_size)
    target_down = target_pcd.voxel_down_sample(voxel_size)

    # Estimate normals
    source_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size*2, max_nn=30))
    target_down.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size*2, max_nn=30))

    # FPFH features
    source_fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        source_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size*5, max_nn=100))
    target_fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        target_down, o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size*5, max_nn=100))

    # RANSAC coarse registration
    ransac_result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
        source_down, target_down, source_fpfh, target_fpfh, True,
        max_correspondence_distance=voxel_size*1.5,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(False),
        ransac_n=4,
        checkers=[
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(voxel_size*1.5)
        ],
        criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(4000000, 500)
    )
    # Ensure the original point clouds have normals (required if ICP is run directly on source_pcd/target_pcd)
    if not source_pcd.has_normals():
        source_pcd.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size*2, max_nn=30))
    if not target_pcd.has_normals():
        target_pcd.estimate_normals(o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size*2, max_nn=30))

    # ICP fine registration
    icp_result = o3d.pipelines.registration.registration_icp(
        source_pcd, target_pcd, voxel_size*0.4, ransac_result.transformation,
        o3d.pipelines.registration.TransformationEstimationPointToPlane(),
        o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)
    )
    return icp_result.transformation

def register_and_transform_grasp_points(source_pcd, target_pcd, grasp_rotation_on_cad, grasp_points_on_cad, voxel_size=0.02):
    """
    Register point clouds and transform CAD grasp points to camera coordinates.
    Args:
        source_pcd: open3d.geometry.PointCloud, source point cloud (CAD model point cloud)
        target_pcd: open3d.geometry.PointCloud, target point cloud (camera point cloud)
        grasp_rotation_on_cad (numpy.ndarray): Grasp pose rotation matrix in CAD model coordinates (3x3)
        grasp_points_on_cad: numpy.ndarray, shape=(3,) or (N,3), grasp point coordinates in CAD model
        voxel_size: Voxel size for downsampling
    Returns:
        grasp_points_in_camera: numpy.ndarray, shape=(3,) or (N,3), grasp points in camera coordinates
        grasp_rotation_in_camera (numpy.ndarray): Grasp pose rotation matrix in camera coordinates (3x3)
        transformation: 4x4 numpy array, registration transformation matrix
    """
    transformation = register_point_clouds(source_pcd, target_pcd, voxel_size)
    grasp_points = np.asarray(grasp_points_on_cad)
    if grasp_points.ndim == 1:
        grasp_points = grasp_points.reshape(1, 3)
    # Convert to homogeneous coordinates
    ones = np.ones((grasp_points.shape[0], 1))
    grasp_points_homo = np.hstack([grasp_points, ones])  # (N,4)
    # Transform
    grasp_points_in_camera_homo = (transformation @ grasp_points_homo.T).T  # (N,4)
    grasp_points_in_camera = grasp_points_in_camera_homo[:, :3]

    # 3. Transform rotation matrix (take only the rotation part of the transformation matrix)
    rotation_part = transformation[:3, :3]
    grasp_rotation_in_camera = rotation_part @ grasp_rotation_on_cad

    if grasp_points_on_cad.ndim == 1:
        return grasp_points_in_camera[0], grasp_rotation_in_camera, transformation
    else:
        return grasp_points_in_camera, grasp_rotation_in_camera, transformation


if __name__ == '__main__':
    import open3d as o3d
    import numpy as np
    
    cad_pcd = o3d.io.read_point_cloud('cad_model.ply')  
    camera_pcd = o3d.io.read_point_cloud('camera_scan.ply')  
    camera_pcd, ind = camera_pcd.remove_statistical_outlier(nb_neighbors=50, std_ratio=0.5)

    # Define standard grasp points and pose on CAD
    grasp_points_on_cad = np.array([
        [-0.122480, 0.068206, -0.142390],
        [0.122098, -0.155091, 0.061945]
    ])

    grasp_rotation_on_cad = np.array([
        [0.61901091,  0.77083935, -0.15044  ],
        [-0.52696196, 0.54967489,  0.64820414],
        [ 0.58235435, -0.32196928, 0.74645776]
    ])

    #  CAD -> camera
    grasp_points_in_camera, grasp_rotation_in_camera, transformation = register_and_transform_grasp_points(
        cad_pcd, camera_pcd, grasp_rotation_on_cad, grasp_points_on_cad, voxel_size=0.004
    )

    print('Transformation matrix:')
    print(transformation)
    print('Grasp rotation in camera coordinate:')
    print(grasp_rotation_in_camera)
    print('Grasp points in camera coordinate:')
    print(grasp_points_in_camera)
