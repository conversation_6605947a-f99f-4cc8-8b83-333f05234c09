#! /usr/bin/env python3
# -*- coding: utf-8 -*-

import math
import h5py
import numpy as np
import sys
import threading
import time
import argparse
import os
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker 

current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(current_dir))

from a2d_sdk.robot import RobotDds

class A2dDataset():
    def __init__(self, data_path: str, batch_size: int = 1, start_index: int = 0, end_index: int = -1,effector_type: str = "gripper") -> None:
        self.batch_size = batch_size
        self.start_index = start_index
        self.end_index = end_index
        self.dataset = []
        self.effector_type = effector_type
        with h5py.File(data_path, "r") as f:
            data = {}
            data['joint'] = np.array(f['action/joint/position'])[self.start_index:self.end_index]
            data['timestamp'] = np.array(f['timestamp'])[self.start_index:self.end_index]
            if self.effector_type == "oy":
                data['effector'] = np.array(f['action/effector/position'])[self.start_index:self.end_index]
                data['effector'][:,0]=(data['effector'][:,0]*180/np.pi+19.48056)* (np.pi / 180)
                data['effector'][:,1]=(180-19.48056-data['effector'][:,1]*180/np.pi)* (np.pi / 180)
                data['effector'][:,2]=(180-19.48056-data['effector'][:,2]*180/np.pi)* (np.pi / 180)
                data['effector'][:,3]=(180-19.48056-data['effector'][:,3]*180/np.pi)* (np.pi / 180)
                data['effector'][:,4]=(180-19.48056-data['effector'][:,4]*180/np.pi)* (np.pi / 180)
                data['effector'][:,5]=(19.48056-data['effector'][:,5]*180/np.pi)* (np.pi / 180)
                data['effector'][:,6]=(data['effector'][:,6]*180/np.pi+19.48056)* (np.pi / 180)
                data['effector'][:,7]=(180-19.48056-data['effector'][:,7]*180/np.pi)* (np.pi / 180)
                data['effector'][:,8]=(180-19.48056-data['effector'][:,8]*180/np.pi)* (np.pi / 180)
                data['effector'][:,9]=(180-19.48056-data['effector'][:,9]*180/np.pi)* (np.pi / 180)
                data['effector'][:,10]=(180-19.48056-data['effector'][:,10]*180/np.pi)* (np.pi / 180)
                data['effector'][:,11]=(19.48056-data['effector'][:,11]*180/np.pi)* (np.pi / 180)
            elif self.effector_type in ["SkillHandS6","gripper"] :
                data['effector'] = np.array(f['action/effector/position'])[self.start_index:self.end_index]
            waist_data = np.array(f['state/waist/position'])[self.start_index:self.end_index]
            data['waist'] = waist_data.copy() 
            data['waist'][:, 1] *= 100  
            data['head'] = np.array(f['state/head/position'])[self.start_index:self.end_index]
            data['wheel'] = np.array(f['action/robot/velocity'])[self.start_index:self.end_index]
            data['timestamp'] = np.array(f['timestamp'])[self.start_index:self.end_index]
            if self.effector_type == "oy":
                data['effector_st'] = np.array(f['state/effector/position'])[self.start_index:self.end_index]
                data['effector_st'][:,0]=(data['effector_st'][:,0]*180/np.pi+19.48056)* (np.pi / 180)
                data['effector_st'][:,1]=(180-19.48056-data['effector_st'][:,1]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,2]=(180-19.48056-data['effector_st'][:,2]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,3]=(180-19.48056-data['effector_st'][:,3]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,4]=(180-19.48056-data['effector_st'][:,4]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,5]=(19.48056-data['effector_st'][:,5]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,6]=(data['effector_st'][:,6]*180/np.pi+19.48056)* (np.pi / 180)
                data['effector_st'][:,7]=(180-19.48056-data['effector_st'][:,7]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,8]=(180-19.48056-data['effector_st'][:,8]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,9]=(180-19.48056-data['effector_st'][:,9]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,10]=(180-19.48056-data['effector_st'][:,10]*180/np.pi)* (np.pi / 180)
                data['effector_st'][:,11]=(19.48056-data['effector_st'][:,11]*180/np.pi)* (np.pi / 180)
            elif self.effector_type in ["SkillHandS6","gripper"] :
                data['effector_st'] = np.array(f['action/effector/position'])[self.start_index:self.end_index]
            data['effector_st'] = np.where(data['effector_st'] < 1.0,
                                        data['effector_st'] * 85 + 35,
                                        data['effector_st'])
            data['joint_st'] = np.array(f['state/joint/position'])[self.start_index:self.end_index]
            waist_data_st = np.array(f['state/waist/position'])[self.start_index:self.end_index]
            data['waist_st'] = waist_data_st.copy() 
            data['waist_st'][:, 1] *= 100 
            data['head_st'] = np.array(f['state/head/position'])[self.start_index:self.end_index]
            self.dataset.append(data)
        self.data_length = len(self.dataset[0]['joint'])
        for key, value in self.dataset[0].items():
            print(f"{key}: {value.shape}")

    def __len__(self):
        return self.data_length

    def __getitem__(self, index: int):
        data = {key: value[index] for key, value in self.dataset[0].items()}
        return data

    def next(self):
        for i in range(0, self.data_length, self.batch_size):
            batch_data = {key: value[i:i + self.batch_size] for key, value in self.dataset[0].items()}
            yield batch_data

class RealtimeData():
    def __init__(self, robot: RobotDds, output_dir: str,effector_type: str = "gripper"):
        self.robot = robot
        self.output_dir = output_dir
        self.effector_type = effector_type
        os.makedirs(self.output_dir, exist_ok=True)
        self.h5_file = h5py.File(os.path.join(self.output_dir, "realtime_data.h5"), 'w') 
        self.h5_file.create_dataset("state/joint/position", (0, 14), maxshape=(None, 14), dtype=np.float32)
        self.h5_file.create_dataset("state/waist/position", (0, 2), maxshape=(None, 2), dtype=np.float32)
        self.h5_file.create_dataset("state/head/position", (0, 2), maxshape=(None, 2), dtype=np.float32)
        if self.effector_type in ["oy", "SkillHandS6"]:
            self.h5_file.create_dataset("state/effector/position", (0, 12), maxshape=(None, 12), dtype=np.float32)
            self.h5_file.create_dataset("action/effector/command", (0, 12), maxshape=(None, 12), dtype=np.float32)
            self.h5_file.create_dataset("error/effector", (0, 12), maxshape=(None,12), dtype=np.float32)
        elif self.effector_type == "gripper":
            self.h5_file.create_dataset("state/effector/position", (0, 2), maxshape=(None, 2), dtype=np.float32)
            self.h5_file.create_dataset("action/effector/command", (0, 2), maxshape=(None, 2), dtype=np.float32)
            self.h5_file.create_dataset("error/effector", (0, 2), maxshape=(None, 2), dtype=np.float32)
        self.h5_file.create_dataset("action/joint/command", (0, 14), maxshape=(None, 14), dtype=np.float32)
        self.h5_file.create_dataset("action/waist/command", (0, 2), maxshape=(None, 2), dtype=np.float32)
        self.h5_file.create_dataset("action/head/command", (0, 2), maxshape=(None, 2), dtype=np.float32)
        self.h5_file.create_dataset("timestamp", (0,), maxshape=(None,), dtype=np.float64)
        self.h5_file.create_dataset("error/joint", (0, 14), maxshape=(None, 14), dtype=np.float32)
        self.h5_file.create_dataset("error/velocity", (0, 2), maxshape=(None, 2), dtype=np.float32)
        self.h5_file.create_dataset("error/head", (0, 2), maxshape=(None, 2), dtype=np.float32)
        self.h5_file.create_dataset("error/waist", (0, 2), maxshape=(None, 2), dtype=np.float32)
    
    def save_data(self, timestamp, joint_position, effector_position,head_position, waist_position,joint_command,effector_command,head_command,waist_command):
        self._append_to_dataset("timestamp", [timestamp])
        self._append_to_dataset("state/joint/position", joint_position)
        self._append_to_dataset("state/effector/position", effector_position)
        self._append_to_dataset("state/head/position", head_position)
        self._append_to_dataset("state/waist/position", waist_position)
        self._append_to_dataset("action/joint/command", joint_command)
        self._append_to_dataset("action/effector/command", effector_command)
        self._append_to_dataset("action/head/command", head_command)
        self._append_to_dataset("action/waist/command", waist_command)
        self.h5_file.flush()

    def save_error(self, collected, realtime,effector_type: str = "gripper"):
        eff_collected = collected.get("effector", None)
        if self.effector_type in ["oy", "SkillHandS6"]:
            eff_error = np.array(eff_collected if eff_collected is not None else np.zeros(12)) \
                    - np.array(realtime["effector"])
        elif self.effector_type == "gripper":
            eff_error = np.array(eff_collected if eff_collected is not None else np.zeros(2)) \
                        - np.array(realtime["effector"])
        error = {
            "waist": np.array(collected.get("waist", np.zeros(2))) - np.array(realtime["waist"]),
            "head": np.array(collected.get("head", np.zeros(2))) - np.array(realtime["head"]),
            "effector": eff_error,
            "joint": np.array(collected.get("joint", np.zeros(14))) - np.array(realtime["joint"]),
        }
        self._append_to_dataset("error/waist", error["waist"])
        self._append_to_dataset("error/head", error["head"])
        self._append_to_dataset("error/effector", error["effector"])
        self._append_to_dataset("error/joint", error["joint"])
        self.h5_file.flush()
    
    def _append_to_dataset(self, dataset_name:str, data):
        dataset = self.h5_file[dataset_name]
        dataset.resize(dataset.shape[0] + 1, axis=0)
        dataset[-1] = data
                
    def close(self):
        if hasattr(self, 'h5_file') and self.h5_file is not None:
            self.h5_file.close()
            self.h5_file = None

def visualize_errors(h5_file_path, output_dir=".",effector_type: str = "gripper"):
    os.makedirs(output_dir, exist_ok=True)
    with h5py.File(h5_file_path, 'r') as f:
        timestamps = f['timestamp'][:]
        joint_errors = f['error/joint'][:]
        effector_errors = f['error/effector'][:]
        head_errors = f['error/head'][:]
        waist_errors = f['error/waist'][:]

    timestamps_seconds = timestamps / 1e9
    start_time = timestamps_seconds[0]
    timestamps_seconds -= start_time
    timestamps_seconds = timestamps_seconds[1:]
    joint_errors = joint_errors[1:, :]
    effector_errors = effector_errors[1:, :]
    head_errors = head_errors[1:, :]
    waist_errors = waist_errors[1:, :]
    fig, axs = plt.subplots(5, 1, figsize=(10, 15))  
    fig.tight_layout(pad=3.0)  
    for i in range(7):  # 关节0-6
        joint_data = joint_errors[:, i]
        data_min = joint_data.min()
        data_max = joint_data.max()
        lower_bound = np.floor(data_min / 0.1) * 0.1  
        upper_bound = np.ceil(data_max / 0.1) * 0.1  
        axs[0].plot(timestamps_seconds, joint_data, label=f'Joint {i} Error', linestyle='-')
    axs[0].set_title('Joint_left Errors')
    axs[0].set_xlabel('Time (seconds)')
    axs[0].set_ylabel('Error')
    axs[0].legend()
    axs[0].xaxis.set_major_locator(ticker.MultipleLocator(1))
    axs[0].xaxis.set_minor_locator(ticker.MultipleLocator(0.2))
    axs[0].yaxis.set_major_locator(ticker.MultipleLocator(0.1))
    axs[0].set_ylim([lower_bound, upper_bound])
    for i in range(7, 14):  # 关节7-13
        joint_data = joint_errors[:, i]
        data_min = joint_data.min()
        data_max = joint_data.max()
        lower_bound = np.floor(data_min / 0.1) * 0.1 
        upper_bound = np.ceil(data_max / 0.1) * 0.1  
        axs[1].plot(timestamps_seconds, joint_data, label=f'Joint {i} Error', linestyle='-')
    axs[1].set_title('Joint_right Errors')
    axs[1].set_xlabel('Time (seconds)')
    axs[1].set_ylabel('Error')
    axs[1].legend()
    axs[1].xaxis.set_major_locator(ticker.MultipleLocator(1))
    axs[1].xaxis.set_minor_locator(ticker.MultipleLocator(0.2))
    axs[1].yaxis.set_major_locator(ticker.MultipleLocator(0.1))
    axs[1].set_ylim([lower_bound, upper_bound])

    if effector_type in ["oy", "SkillHandS6"]:
        labels = [f"hand_{i}" for i in range(1, 13)]
        for col, label in enumerate(labels):
            axs[2].plot(timestamps_seconds, effector_errors[:, col], label=label)
    elif effector_type == "gripper":
        labels = ["Gripper Left", "Gripper Right"]
        for col, label in enumerate(labels):
            axs[2].plot(timestamps_seconds, effector_errors[:, col], label=label)

    axs[2].set_title('Gripper Errors')
    axs[2].set_xlabel('Time (seconds)')
    axs[2].set_ylabel('Error')
    axs[2].legend()
    axs[2].xaxis.set_major_locator(ticker.MultipleLocator(1))
    axs[2].xaxis.set_minor_locator(ticker.MultipleLocator(0.2))
    axs[2].grid(which='both', linestyle='--', linewidth=0.5)

    he_data_min = head_errors.min()
    he_data_max = head_errors.max() 
    he_lower_bound = np.floor(he_data_min / 0.1) * 0.1 
    he_upper_bound = np.ceil(he_data_max / 0.1) * 0.1  
    axs[3].plot(timestamps_seconds, head_errors[:, 0], label='Head Pitch')
    axs[3].plot(timestamps_seconds, head_errors[:, 1], label='Head Yaw')
    axs[3].set_title('Head Errors')
    axs[3].set_xlabel('Time (seconds)')
    axs[3].set_ylabel('Error')
    axs[3].legend()
    axs[3].xaxis.set_major_locator(ticker.MultipleLocator(1))
    axs[3].xaxis.set_minor_locator(ticker.MultipleLocator(0.2))
    axs[3].yaxis.set_major_locator(ticker.MultipleLocator(0.1))
    axs[3].set_ylim([he_lower_bound, he_upper_bound])
    axs[3].grid(which='both', linestyle='--', linewidth=0.5)

    wa_data_min = waist_errors.min()
    wa_data_max = waist_errors.max()
    wa_lower_bound = np.floor(wa_data_min / 0.1) * 0.1  
    wa_upper_bound = np.ceil(wa_data_max / 0.1) * 0.1  
    axs[4].plot(timestamps_seconds, waist_errors[:, 0], label='Waist Motor 1')
    axs[4].plot(timestamps_seconds, waist_errors[:, 1], label='Waist Motor 2')
    axs[4].set_title('Waist Errors')
    axs[4].set_xlabel('Time (seconds)')
    axs[4].set_ylabel('Error')
    axs[4].legend()
    axs[4].xaxis.set_major_locator(ticker.MultipleLocator(1))
    axs[4].xaxis.set_minor_locator(ticker.MultipleLocator(0.2))
    axs[4].yaxis.set_major_locator(ticker.MultipleLocator(0.1))
    axs[4].set_ylim([wa_lower_bound, wa_upper_bound])
    axs[4].grid(which='both', linestyle='--', linewidth=0.5)

    error_plot_path = os.path.join(output_dir, 'error_plots1.png')
    plt.savefig(error_plot_path)
    print(f"Error plots saved at {error_plot_path}")
    plt.close()

def visualize_data_with_real(h5_file_path, real_file_path, output_dir=".",effector_type: str = "gripper"):
    os.makedirs(output_dir, exist_ok=True)
    with h5py.File(h5_file_path, 'r') as f:
        timestamps = f['timestamp'][:]
        action_joint_position = f['action/joint/position'][1:]
        state_joint_position = f['state/joint/position'][1:]
        effector_positions_action = f['action/effector/position'][1:]
        effector_positions_state = f['state/effector/position'][1:]
        head_positions_action = f['state/head/position'][1:]
        head_positions_state = f['state/head/position'][1:]
        waist_positions_action = f['state/waist/position'][1:]
        waist_positions_action[:, 1] *= 100
        waist_positions_state = f['state/waist/position'][1:]
        waist_positions_state[:, 1] *= 100
    with h5py.File(real_file_path, 'r') as f:
        joint_positions_real = f['state/joint/position'][1:]
        effector_positions_real = f['state/effector/position'][1:]
        head_positions_real = f['state/head/position'][1:]
        waist_positions_real = f['state/waist/position'][1:]
        joint_positions_action_real = f['action/joint/command'][1:]
        effector_positions_action_real = f['action/effector/command'][1:]
        head_positions_action_real = f['action/head/command'][1:]
        waist_positions_action_real = f['action/waist/command'][1:]
    real_length = min(len(joint_positions_real), len(timestamps))
    timestamps_seconds = timestamps / 1e9
    start_time = timestamps_seconds[0]
    timestamps_seconds -= start_time
    if len(timestamps_seconds) > len(joint_positions_real):
        timestamps_seconds = timestamps_seconds[:len(joint_positions_real)]
    elif len(timestamps_seconds) < len(joint_positions_real):
        joint_positions_real = joint_positions_real[:len(timestamps_seconds)]

    fig1, axes1 = plt.subplots(7, 2, figsize=(14, 14))
    axes1 = axes1.flatten()
    for i in range(min(action_joint_position.shape[1], len(axes1))):  
        ax = axes1[i]
        joint_data = action_joint_position[:real_length, i]
        state_data = state_joint_position[:real_length, i]
        replay_data = joint_positions_real[:real_length, i]
        action_real_data = joint_positions_action_real[:real_length, i]  
        data_min = min(joint_data.min(), state_data.min(), replay_data.min(), action_real_data.min())
        data_max = max(joint_data.max(), state_data.max(), replay_data.max(), action_real_data.max())
        data_range = data_max - data_min
        if data_range < 0.01:
            lower_bound = np.floor(data_min / 0.1) * 0.1  
            upper_bound = np.ceil(data_max / 0.1) * 0.1  
        else:
            lower_bound = data_min  
            upper_bound = data_max  
        ax.plot(timestamps_seconds, joint_data, label=f'Joint {i} Action', linestyle='-')
        ax.plot(timestamps_seconds, state_data, label=f'Joint {i} State', linestyle='--')
        ax.plot(timestamps_seconds, replay_data, label=f'Joint {i} Replay_state', linestyle='-.')
        ax.plot(timestamps_seconds, action_real_data, label=f'Joint {i} Action (Real)', linestyle=':')
        ax.set_title(f'Joint {i} Position: Action vs State vs Replay_action vs Replay_state')
        ax.set_xlabel('Time (seconds)')
        ax.set_ylabel('Position')
        ax.legend()
        ax.xaxis.set_major_locator(ticker.MultipleLocator(1))  
        ax.xaxis.set_minor_locator(ticker.MultipleLocator(0.2))  
        ax.yaxis.set_major_locator(ticker.MultipleLocator(0.1))  
        ax.set_ylim(lower_bound, upper_bound) 

    fig2, axes2 = plt.subplots(3,1, figsize=(12, 12))
    axes2 = axes2.flatten()

    ax = axes2[0]
    mask_action_real = effector_positions_action_real[:real_length, :] != 0
    if effector_type in ["oy", "SkillHandS6"]:
        for col in range(12):
            if np.any(mask_action_real[:, col]):
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_action[:real_length, col][mask_action_real[:, col]],
                    label=f'Hand {col+1} Action',
                    linestyle='--'
                )
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_state[:real_length, col][mask_action_real[:, col]],
                    label=f'Hand {col+1} State',
                    linestyle='-'
                )
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_real[:real_length, col][mask_action_real[:, col]],
                    label=f'Hand {col+1} Replay_state',
                    linestyle='-.'
                )
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_action_real[:real_length, col][mask_action_real[:, col]],
                    label=f'Hand {col+1} Action (Real)',
                    linestyle=':'
                )
    elif effector_type == "gripper":
        sides = ["Left", "Right"]
        for col, side in enumerate(sides):
            if np.any(mask_action_real[:, col]):
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_action[:real_length, col][mask_action_real[:, col]],
                    label=f'Gripper {side} Action',
                    linestyle='--'
                )
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_state[:real_length, col][mask_action_real[:, col]],
                    label=f'Gripper {side} State',
                    linestyle='-'
                )
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_real[:real_length, col][mask_action_real[:, col]],
                    label=f'Gripper {side} Replay_state',
                    linestyle='-.'
                )
                ax.plot(
                    timestamps_seconds[mask_action_real[:, col]],
                    effector_positions_action_real[:real_length, col][mask_action_real[:, col]],
                    label=f'Gripper {side} Action (Real)',
                    linestyle=':'
                )
    ax.set_title('Effector Positions: Action vs State vs Replay_action vs Replay_state')
    ax.set_xlabel('Time (seconds)')
    ax.set_ylabel('Position')
    ax.legend()
    ax.xaxis.set_major_locator(ticker.MultipleLocator(1))
    ax.xaxis.set_minor_locator(ticker.MultipleLocator(0.2))

    ax = axes2[1]
    ax.plot(timestamps_seconds, head_positions_action[:real_length, 0], label='Head Pitch Action', linestyle='--')
    ax.plot(timestamps_seconds, head_positions_state[:real_length, 0], label='Head Pitch State', linestyle='-')
    ax.plot(timestamps_seconds, head_positions_real[:real_length, 0], label='Head Pitch Replay_state', linestyle='-.')
    ax.plot(timestamps_seconds, head_positions_action_real[:real_length, 0], label='Head Pitch Action (Real)', linestyle=':')
    ax.plot(timestamps_seconds, head_positions_action[:real_length, 1], label='Head Yaw Action', linestyle='--')
    ax.plot(timestamps_seconds, head_positions_state[:real_length, 1], label='Head Yaw State', linestyle='-')
    ax.plot(timestamps_seconds, head_positions_real[:real_length, 1], label='Head Yaw Replay_state', linestyle='-.')
    ax.plot(timestamps_seconds, head_positions_action_real[:real_length, 1], label='Head Yaw Action (Real)', linestyle=':')
    ax.set_title('Head Positions: Action vs State vs Replay_action vs Replay_state')
    ax.set_xlabel('Time (seconds)')
    ax.set_ylabel('Position')
    ax.legend()
    ax.xaxis.set_major_locator(ticker.MultipleLocator(1))
    ax.xaxis.set_minor_locator(ticker.MultipleLocator(0.2))

    ax = axes2[2]
    ax.plot(timestamps_seconds, waist_positions_action[:real_length, 0], label='Waist Action (Motor 1)', linestyle='--')
    ax.plot(timestamps_seconds, waist_positions_state[:real_length, 0], label='Waist State (Motor 1)', linestyle='-')
    ax.plot(timestamps_seconds, waist_positions_real[:real_length, 0], label='Waist Replay_state (Motor 1)', linestyle='-.')
    ax.plot(timestamps_seconds, waist_positions_action_real[:real_length, 0], label='Waist Action (Real, Motor 1)', linestyle=':')
    ax.plot(timestamps_seconds, waist_positions_action[:real_length, 1], label='Waist Action (Motor 2)', linestyle='--')
    ax.plot(timestamps_seconds, waist_positions_state[:real_length, 1], label='Waist State (Motor 2)', linestyle='-')
    ax.plot(timestamps_seconds, waist_positions_real[:real_length, 1], label='Waist Replay_state (Motor 2)', linestyle='-.')
    ax.plot(timestamps_seconds, waist_positions_action_real[:real_length, 1], label='Waist Action (Real, Motor 2)', linestyle=':')
    ax.set_title('Waist Positions: Action vs State vs Replay_action vs Replay_state')
    ax.set_xlabel('Time (seconds)')
    ax.set_ylabel('Position (cm)')
    ax.legend()
    ax.xaxis.set_major_locator(ticker.MultipleLocator(1))
    ax.xaxis.set_minor_locator(ticker.MultipleLocator(0.2))

    plt.tight_layout()  
    output_path1 = os.path.join(output_dir, "joint_error_plots.png")
    plt.figure(fig1.number) 
    plt.savefig(output_path1)
    plt.close(fig1)  
    output_path2 = os.path.join(output_dir, "other_error_plots.png")
    plt.figure(fig2.number) 
    plt.savefig(output_path2)
    plt.close(fig2)  

    print(f"Error plots saved at {output_path1} and {output_path2}")

def replay(robot: RobotDds, stop_event: threading.Event, args):
    effector_type=args.t
    time.sleep(1)
    base_name = os.path.splitext(os.path.basename(args.h5))[0]
    output_dir = os.path.join(os.path.dirname(args.h5), base_name + "_error") 
    os.makedirs(output_dir, exist_ok=True)
    realtime_data = RealtimeData(robot=robot, output_dir=output_dir, effector_type=effector_type) 
    dataset = A2dDataset(data_path=args.h5, start_index=args.start, end_index=args.end, effector_type=effector_type)
    last_timestamp = 0
    for data in dataset.next():
        timestamp = data['timestamp'][0]
        joint_positions = data['joint']
        collected = {
            "joint": data.get('joint_st', [None])[0],
            "effector": data.get('effector_st', [None])[0],
            "head": data.get('head_st', [None])[0],
            "waist": data.get('waist_st', [None])[0],
        }
        if args.t in ["oy", "SkillHandS6"]:
            current_effector = robot.hand_joint_states()[0] \
                if robot.hand_joint_states()[0] and len(robot.hand_joint_states()[0]) == 12 \
                else np.zeros(12)
        elif args.t == "gripper":
            current_effector = robot.gripper_states()[0] \
                if robot.gripper_states()[0] and len(robot.gripper_states()[0]) == 2 \
                else np.zeros(2)
        realtime = {
            "joint": robot.arm_joint_states()[0] if robot.arm_joint_states()[0] else np.zeros(14),
            "effector": current_effector,
            "head": (np.array(robot.head_joint_states()[0]) * (np.pi / 180)) if (robot.head_joint_states()[0] and len(robot.head_joint_states()[0]) == 2) else np.zeros(2),
            "waist": [
                robot.waist_joint_states()[0][0] if (robot.waist_joint_states()[0][0])  else 0.0,
                robot.waist_joint_states()[0][1]*100 if (robot.waist_joint_states()[0][1]) else 0.0,
            ],
        }
        print (f"effector:{realtime['effector']}")
        if last_timestamp == 0:
            last_timestamp = data['timestamp'][0]
            time.sleep(2)
            robot.reset(arm_positions=data['joint'][0].tolist(), gripper_positions=data['effector'][0].tolist(),head_positions=data['head'][0].tolist(),waist_positions=data['waist'][0].tolist())
            print("robot.reset is finish")
        else:
            if data['timestamp'][0] - last_timestamp > 0:
                time_diff = (timestamp - last_timestamp) / 1000000000.0 
                action_start_time = time.time()
                robot.move_arm(joint_positions[0].tolist())
                if len(data['waist']) > 0 and len(data['head']) > 0:
                        time.sleep(0.005) 
                        robot.move_head_and_waist(data['head'][0].tolist(),data['waist'][0].tolist())
                print(f"Gripper command: {data['effector'][0].tolist()}")
                if effector_type in ["oy", "SkillHandS6"]:
                    robot.move_hand(data['effector'][0].tolist())
                elif effector_type == "gripper":
                    robot.move_gripper(data['effector'][0].tolist())
                if data['wheel'][0].shape != (2,):
                    robot.move_wheel(data['wheel'][0], 0.0)
                else:
                    robot.move_wheel(data['wheel'][0][1], data['wheel'][0][1])
                action_end_time = time.time()
                action_time = action_end_time - action_start_time
                sleep_time = max(0, time_diff - action_time)
                time.sleep(sleep_time)
                last_timestamp = data['timestamp'][0]
                effector_command = data['effector'][0]
                joint_command = joint_positions[0]  
                denorm_effector_command = list(effector_command) 
                if denorm_effector_command[0] <= 1.0:
                    denorm_effector_command[0] = denorm_effector_command[0] * 85 + 35
                if denorm_effector_command[1] <= 1.0:
                    denorm_effector_command[1] = denorm_effector_command[1] * 85 + 35
                head_command = data['head'][0]  
                waist_command = data['waist'][0]  
                realtime_data.save_data(
                    timestamp=timestamp,
                    joint_position=realtime["joint"],
                    effector_position=realtime["effector"],
                    head_position=realtime["head"],
                    waist_position=realtime["waist"],
                    joint_command=joint_command,
                    effector_command=denorm_effector_command,
                    head_command=head_command,
                    waist_command=waist_command
                )
                realtime_data.save_error(collected, realtime)
                
    realtime_data.close()
    print("Generating error plots...")
    visualize_errors(h5_file_path=os.path.join(output_dir, "realtime_data.h5"), output_dir=output_dir, effector_type=args.t)
    print("Generating error plots1 is finish.")
    visualize_data_with_real(h5_file_path=args.h5,real_file_path=os.path.join(output_dir, "realtime_data.h5"),output_dir=output_dir, effector_type=args.t)
    print("Generating error plots2 is finish.")
    robot.shutdown()
    stop_event.set()
    

def main(args=None):
    if args is None:
        parser = argparse.ArgumentParser()
        parser.add_argument("--h5", type=str, required=True)
        parser.add_argument("--start", type=int, default=0, help="start frame index")
        parser.add_argument("--end", type=int, default=-1, help="end frame index")
        parser.add_argument("--t", type=str, default="gripper", help="type of effector")
        args = parser.parse_args()
    robot = RobotDds()
    stop_event = threading.Event()
    control_thread = threading.Thread(
       target=replay, args=(robot, stop_event, args), daemon=True
    )
    control_thread.start()

    try:
        control_thread.join()
    except KeyboardInterrupt:
        print("收到键盘中断，正在关闭节点")
    finally:
        stop_event.set()
        control_thread.join()
        print("程序退出")
        sys.exit(0)

if __name__ == '__main__':
    main()
