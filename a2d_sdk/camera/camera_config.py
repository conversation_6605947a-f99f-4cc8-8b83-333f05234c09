USE_ZMQ = True
USE_ROS = not USE_ZMQ
SEND_COMPRESSED_IMAGE = False
DEFAULT_ROS_DOMAIN_ID = "88"

ENABLE_PTP_CLOCK = False

# zmq comm
IP_A2D = "***********"
# IP_A2D = "**************"
BASE_PORT = 6555

DEPLOY_MODE_53 = "5+3"
DEPLOY_MODE_71 = "7+1"
DEPLOY_MODE = DEPLOY_MODE_53

CAMERA_SHAPE = {
    "head": (720, 1280, 3),
    "head_depth": (720, 1280),
    "hand_left": (480, 848, 3),
    "hand_left_depth": (480, 848),
    "hand_right": (480, 848, 3),
    "hand_right_depth": (480, 848),
    "back_left_fisheye": (1537, 1920, 3),
    "hand_left_fisheye": (1537, 1920, 3),
    "hand_right_fisheye": (1537, 1920, 3),
    "back_right_fisheye": (1537, 1920, 3),
    "head_center_fisheye": (1537, 1920, 3),
    "head_left_fisheye": (1537, 1920, 3),
    "head_right_fisheye": (1537, 1920, 3)
}

REALSENSE_CAMERA_INDEX = {
    0 : "head",
    1 : "hand_left",
    2 : "hand_right"
}

FISHEYE_CAMERA_INDEX = {
    0 : "back_left_fisheye",
    1 : "hand_left_fisheye",
    2 : "hand_right_fisheye",
    3 : "back_right_fisheye",
    4 : "head_center_fisheye",
    5 : "head_left_fisheye",
    6 : "head_right_fisheye"
}

CAMERA_DDS_TOPIC = {
    "head" : "/camera/head_color",
    "hand_left" : "/camera/hand_left_color",
    "hand_right" : "/camera/hand_right_color",
    "head_depth" : "/camera/head_depth",
    "hand_left_fisheye" : "/camera/hand_left_fisheye",
    "hand_right_fisheye" : "/camera/hand_right_fisheye",
    "back_left_fisheye" : "/camera/back_left_fisheye",
    "back_right_fisheye" : "/camera/back_right_fisheye",
    "head_center_fisheye" : "/camera/head_center_fisheye",
    "head_left_fisheye" : "/camera/head_left_fisheye",
    "head_right_fisheye" : "/camera/head_right_fisheye"
}
