import cosine_bus
import numpy as np
from a2d_sdk.camera.camera_config import CAMERA_DDS_TOPIC
import cv2
class CameraReceiverCosine:
    def __init__(self, camera_group: list[str]):
        self.camera_group = camera_group
        self.receiver_dict = {}
        for camera_name in camera_group:
            # 为每个相机创建一个图像订阅者
            topic = CAMERA_DDS_TOPIC[camera_name] if camera_name in CAMERA_DDS_TOPIC else camera_name
            self.receiver_dict[topic] = cosine_bus.image_subscriber(f"{topic}", cosine_bus.QosReliability.BEST_EFFORT, 40)
        
    def get_latest_image(self, camera_name: str) -> tuple[np.ndarray, int]:
        """获取指定相机的最新图像
        
        Args:
            camera_name: 相机名称
            
        Returns:
            np.ndarray: 图像数据,如果没有收到有效数据则返回None
        """
        camera_name = self.get_topic(camera_name)
        if camera_name not in self.receiver_dict:
            return None, None
            
        receiver = self.receiver_dict[camera_name]
        packet = receiver.read_latest_frame()
        
        if packet is None or packet.type != cosine_bus.PacketType.IMAGE:
            return None, None
            
        image_data = packet.get_image_data()
        if image_data is None:
            return None, None
        else:
            encoding = packet.encoding_format
            color_format = packet.color_format
            width = packet.image_width
            height = packet.image_height
            
            try:
                if encoding in [cosine_bus.EncodingFormat.JPEG, cosine_bus.EncodingFormat.PNG]:
                    frame = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
                    if frame is not None:
                        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                        return frame, packet.send_timestamp
                    else:
                        print(f"Failed to decode {encoding} image")
                else:
                    if color_format == cosine_bus.ColorFormat.RS2_FORMAT_Z16:
                        image_depth = np.frombuffer(image_data, dtype=np.uint16)
                        frame = image_depth.reshape(height, width, 1)
                        return frame, packet.send_timestamp
                    else:
                        frame = np.frombuffer(image_data, dtype=np.uint8).reshape(height, width, 3)
                        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                        return frame, packet.send_timestamp
            except Exception as e:
                print(f"处理图像失败: {e}")
        return None, None
        
    def get_latest_packet(self, camera_name: str):
        """获取指定相机的最新图像包
        
        Args:
            camera_name: 相机名称
            
        Returns:
            cosine_bus.Packet: 图像包,如果没有收到有效数据则返回None
        """
        camera_name = self.get_topic(camera_name)
        if camera_name not in self.receiver_dict:
            return None
        return self.receiver_dict[camera_name].read_latest_frame()
    
    def get_fps(self, camera_name: str):
        """获取指定相机的fps
        
        Args:
            camera_name: 相机名称
            
        Returns:
            float: fps,如果没有收到有效数据则返回0
        """
        camera_name = self.get_topic(camera_name)
        if camera_name not in self.receiver_dict:
            return 0
        return self.receiver_dict[camera_name].get_fps()

    def get_latency_stats(self, camera_name: str, window_seconds: float = 5.0):
        """获取指定相机的延迟统计信息
        
        Args:
            camera_name: 相机名称
            window_seconds: 窗口时间,单位为秒
            
        Returns:
            dict: 延迟统计信息,如果没有收到有效数据则返回None
        """
        camera_name = self.get_topic(camera_name)
        if camera_name not in self.receiver_dict:
            return 0
        return self.receiver_dict[camera_name].get_latency_stats(window_seconds)
    def close(self):
        """关闭所有接收器"""
        self.receiver_dict.clear()
    def get_topic(self, camera_name: str):
        return CAMERA_DDS_TOPIC[camera_name] if camera_name in CAMERA_DDS_TOPIC else camera_name

    def get_packet_nearest(self, camera_name: str, timestamp_ns: int):
        """获取指定时间戳最近的图像包
        
        Args:
            camera_name: 相机名称
            timestamp_ns: 时间戳(纳秒)
            
        Returns:
            cosine_bus.Packet: 图像包,如果没有收到有效数据则返回None
        """
        camera_name = self.get_topic(camera_name)
        if camera_name not in self.receiver_dict:
            return None
        return self.receiver_dict[camera_name].read_frame_nearest(timestamp_ns)

    def get_image_nearest(self, camera_name: str, timestamp_ns: int) -> np.ndarray:
        """获取指定时间戳最近的图像
        
        Args:
            camera_name: 相机名称
            timestamp_ns: 时间戳(纳秒)
            
        Returns:
            np.ndarray: 图像数据,如果没有收到有效数据则返回None
        """
        packet = self.get_packet_nearest(camera_name, timestamp_ns)
        if packet is None or packet.type != cosine_bus.PacketType.IMAGE:
            return None, None
            
        image_data = packet.get_image_data()
        if image_data is None:
            return None, None
        else:
            encoding = packet.encoding_format
            color_format = packet.color_format
            width = packet.image_width
            height = packet.image_height
            
            try:
                if encoding in [cosine_bus.EncodingFormat.JPEG, cosine_bus.EncodingFormat.PNG]:
                    frame = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
                    if frame is not None:
                        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                        return frame, packet.send_timestamp
                    else:
                        print(f"Failed to decode {encoding} image")
                else:
                    if color_format == cosine_bus.ColorFormat.RS2_FORMAT_Z16:
                        image_depth = np.frombuffer(image_data, dtype=np.uint16)
                        frame = image_depth.reshape(height, width, 1)
                        return frame, packet.send_timestamp
                    else:
                        frame = np.frombuffer(image_data, dtype=np.uint8).reshape(height, width, 3)
                        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                        return frame, packet.send_timestamp
            except Exception as e:
                print(f"处理图像失败: {e}")
        return None