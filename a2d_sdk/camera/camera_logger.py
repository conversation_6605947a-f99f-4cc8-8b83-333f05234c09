import logging
import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))

def setup_logger(logger_name):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)
    # integrate with other projects, write log to parent directory
    log_dir = os.path.join(current_dir, "../log")
    if os.path.exists(log_dir):
        file_handler = logging.FileHandler(f"{log_dir}/{logger_name}.txt")
    else:
        file_handler = logging.FileHandler(f"{logger_name}.txt")
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.ERROR)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger

camera_logger = setup_logger("camera")