from __future__ import annotations

import numpy as np
import time
import sys
import os
try:
    import pyrealsense2 as rs
    print("pyrealsense2 is installed")
except ImportError:
    print("pyrealsense2 is not installed")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.append(os.path.join(current_dir, "3rdparty/librealsense-2.56.2/build/Release"))
    import pyrealsense2 as rs
    assert rs.__version__ == "2.56.2"

from a2d_sdk.camera.camera_logger import camera_logger

class RealSense:

    def __init__(self, cfg, fps=30):
        assert cfg is not None
        camera_logger.info(f"init realsense {cfg['name']} with params {cfg}")

        self._raw_config = cfg
        self.sn = int(cfg["SN"])
        self.width = cfg["width"]
        self.height = cfg["height"]
        # self.src = cfg["src"]
        self.name = cfg["name"]

        # Configure the realsense pipeline
        self.pipeline = rs.pipeline()
        self.config = rs.config()
        self.config.enable_device(str(self.sn))
        self.config.enable_stream(
            rs.stream.depth, self.width, self.height, rs.format.z16, fps
        )
        self.config.enable_stream(
            rs.stream.color, self.width, self.height, rs.format.rgb8, fps
        )
        self.profile = self.pipeline.start(self.config)

        self.align_to = rs.stream.color
        self.align = rs.align(self.align_to)
        self.fps = fps
        time.sleep(2)
    
    def __del__(self):
        if self.pipeline is not None:
            self.pipeline.stop()

    def get_frame(self):
        try:
            for _ in range(10000):
                # Wait for a coherent pair of frames: depth and color
                frames = self.pipeline.wait_for_frames()
                aligned_frames = self.align.process(frames)
                color_frame = aligned_frames.get_color_frame()
                if not color_frame:
                    continue
                else:
                    color_image = np.asanyarray(color_frame.get_data())
                    return color_image, color_frame.get_timestamp() / 1e3

            camera_logger.error(f"RealSense {self.name} get frame timeout")
            return None, None
        except Exception as e:
            camera_logger.error(f"RealSense {self.name} get frame error: {e}")
            return None, None

    # get color and depth frames at the same time
    def get_frames(self):
        try:
            for _ in range(10000):
                # Wait for a coherent pair of frames: depth and color
                frames = self.pipeline.wait_for_frames()
                aligned_frames = self.align.process(frames)
                color_frame = aligned_frames.get_color_frame()
                depth_frame = aligned_frames.get_depth_frame()
                if not color_frame or not depth_frame:
                    continue
                else:
                    color_image = np.asanyarray(color_frame.get_data())
                    depth_image = np.asanyarray(depth_frame.get_data())
                    return color_image, depth_image, color_frame.get_timestamp() / 1e3

            camera_logger.error(f"RealSense {self.name} get frame timeout")
            return None, None, None
        except Exception as e:
            camera_logger.error(f"RealSense {self.name} get frame error: {e}")
            return None, None, None


if __name__ == "__main__":
    cfg_455 = {
        "name": "test",
        "SN": "311322303362",
        "width": 1280,
        "height": 720,
        "src": "1",
    }
    rs_455 = RealSense(cfg_455)
    image, timestamp = rs_455.get_frame()
    print(image.shape)
    print(timestamp)

    import cv2
    cv2.imwrite("455_test.jpg", image)

    cfg_405 = {
        "name": "test",
        "SN": "230322276282",
        "width": 848,
        "height": 480,
        "src": "1",
    }
    rs_405 = RealSense(cfg_405)
    image, timestamp = rs_405.get_frame()
    print(image.shape)
    print(timestamp)

    cv2.imwrite("405_test.jpg", image)
