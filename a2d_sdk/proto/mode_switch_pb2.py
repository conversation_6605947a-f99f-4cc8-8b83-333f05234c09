# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mode_switch.proto

from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import image_preprocess_pb2 as image__preprocess__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mode_switch.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x11mode_switch.proto\x1a\x16image_preprocess.proto\"\x9f\x01\n\x12HybridDeployConfig\x12\x0f\n\x07use_zmq\x18\x01 \x01(\x08\x12 \n\x18use_ros_compressed_image\x18\x02 \x01(\x08\x12\x11\n\tserver_ip\x18\x03 \x01(\t\x12\x15\n\rros_domain_id\x18\x04 \x01(\x05\x12\x16\n\x0euse_cosine_sdk\x18\x05 \x01(\x08\x12\x14\n\x0c\x63\x61mera_model\x18\x06 \x01(\t\"\x87\x01\n\x11ModeSwitchRequest\x12\x13\n\x04mode\x18\x01 \x01(\x0e\x32\x05.Mode\x12*\n\x0bimg_preproc\x18\x02 \x03(\x0b\x32\x15.ImagePreprocessParam\x12\x31\n\x14hybrid_deploy_config\x18\x03 \x01(\x0b\x32\x13.HybridDeployConfig\"9\n\x12ModeSwitchResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x12\n\nerror_info\x18\x02 \x01(\t*8\n\x04Mode\x12\x08\n\x04IDLE\x10\x00\x12\x13\n\x0f\x44\x41TA_COLLECTION\x10\x01\x12\x11\n\rHYBRID_DEPLOY\x10\x02\x62\x06proto3'
  ,
  dependencies=[image__preprocess__pb2.DESCRIPTOR,])

_MODE = _descriptor.EnumDescriptor(
  name='Mode',
  full_name='Mode',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IDLE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DATA_COLLECTION', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HYBRID_DEPLOY', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=404,
  serialized_end=460,
)
_sym_db.RegisterEnumDescriptor(_MODE)

Mode = enum_type_wrapper.EnumTypeWrapper(_MODE)
IDLE = 0
DATA_COLLECTION = 1
HYBRID_DEPLOY = 2



_HYBRIDDEPLOYCONFIG = _descriptor.Descriptor(
  name='HybridDeployConfig',
  full_name='HybridDeployConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='use_zmq', full_name='HybridDeployConfig.use_zmq', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_ros_compressed_image', full_name='HybridDeployConfig.use_ros_compressed_image', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_ip', full_name='HybridDeployConfig.server_ip', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ros_domain_id', full_name='HybridDeployConfig.ros_domain_id', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_cosine_sdk', full_name='HybridDeployConfig.use_cosine_sdk', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='camera_model', full_name='HybridDeployConfig.camera_model', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=46,
  serialized_end=205,
)


_MODESWITCHREQUEST = _descriptor.Descriptor(
  name='ModeSwitchRequest',
  full_name='ModeSwitchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='mode', full_name='ModeSwitchRequest.mode', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='img_preproc', full_name='ModeSwitchRequest.img_preproc', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hybrid_deploy_config', full_name='ModeSwitchRequest.hybrid_deploy_config', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=208,
  serialized_end=343,
)


_MODESWITCHRESPONSE = _descriptor.Descriptor(
  name='ModeSwitchResponse',
  full_name='ModeSwitchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='ModeSwitchResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_info', full_name='ModeSwitchResponse.error_info', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=345,
  serialized_end=402,
)

_MODESWITCHREQUEST.fields_by_name['mode'].enum_type = _MODE
_MODESWITCHREQUEST.fields_by_name['img_preproc'].message_type = image__preprocess__pb2._IMAGEPREPROCESSPARAM
_MODESWITCHREQUEST.fields_by_name['hybrid_deploy_config'].message_type = _HYBRIDDEPLOYCONFIG
DESCRIPTOR.message_types_by_name['HybridDeployConfig'] = _HYBRIDDEPLOYCONFIG
DESCRIPTOR.message_types_by_name['ModeSwitchRequest'] = _MODESWITCHREQUEST
DESCRIPTOR.message_types_by_name['ModeSwitchResponse'] = _MODESWITCHRESPONSE
DESCRIPTOR.enum_types_by_name['Mode'] = _MODE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HybridDeployConfig = _reflection.GeneratedProtocolMessageType('HybridDeployConfig', (_message.Message,), {
  'DESCRIPTOR' : _HYBRIDDEPLOYCONFIG,
  '__module__' : 'mode_switch_pb2'
  # @@protoc_insertion_point(class_scope:HybridDeployConfig)
  })
_sym_db.RegisterMessage(HybridDeployConfig)

ModeSwitchRequest = _reflection.GeneratedProtocolMessageType('ModeSwitchRequest', (_message.Message,), {
  'DESCRIPTOR' : _MODESWITCHREQUEST,
  '__module__' : 'mode_switch_pb2'
  # @@protoc_insertion_point(class_scope:ModeSwitchRequest)
  })
_sym_db.RegisterMessage(ModeSwitchRequest)

ModeSwitchResponse = _reflection.GeneratedProtocolMessageType('ModeSwitchResponse', (_message.Message,), {
  'DESCRIPTOR' : _MODESWITCHRESPONSE,
  '__module__' : 'mode_switch_pb2'
  # @@protoc_insertion_point(class_scope:ModeSwitchResponse)
  })
_sym_db.RegisterMessage(ModeSwitchResponse)


# @@protoc_insertion_point(module_scope)
