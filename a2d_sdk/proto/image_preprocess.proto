syntax = "proto3";

/* 传感器安装位置 */
enum MountPos {
    head = 0;
    hand_left = 1;
    hand_right = 2;
    back_left_fisheye = 3;
    hand_left_fisheye = 4;
    hand_right_fisheye = 5;
    back_right_fisheye = 6;
    head_center_fisheye = 7;
    head_left_fisheye = 8;
    head_right_fisheye = 9;
}

/* 传感器类型 */
enum SensorType {
    RGBD = 0;
    FISHEYE = 1;
}

/* 插值算法 */
enum Interpolation {
    NEAREST = 0;
    LINEAR = 1;
    CUBIC = 2;
}

/* 裁剪参数 */
message CropParam {
    bool enable = 1;
    uint32 l = 2;
    uint32 t = 3;
    uint32 r = 4;
    uint32 b = 5;
}

/* 调整图像尺寸参数 */
message ResizeParam {
    bool enable = 1;
    uint32 w = 2;
    uint32 h = 3;
    Interpolation interp = 4;
}

/* 通道交换参数 */
message SwapChannelParam {
    bool enable = 1;
    uint32 order1 = 2;
    uint32 order2 = 3;
    uint32 order3 = 4;
}

/* 鱼眼图像去畸变参数 */
message FisheyeUndistortParam {
    bool enable = 1;
    float bal = 2; // 畸变平衡系数[0.0, 1.0]
    Interpolation interp = 3;
}

/* 图像预处理参数 */
message ImagePreprocessParam {
    MountPos pos = 1;
    FisheyeUndistortParam undistort = 2;    
    CropParam crop = 3;
    ResizeParam resize = 4;
    SwapChannelParam permute = 5;
    int32 FrameRate = 6;
    bool depth_enable = 7;
}
