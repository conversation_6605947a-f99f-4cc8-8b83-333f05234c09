syntax = "proto3";

import "image_preprocess.proto";

enum Mode {
    IDLE = 0;
    DATA_COLLECTION = 1;
    HYBRID_DEPLOY = 2;
}

message HybridDeployConfig {
    bool use_zmq = 1;
    bool use_ros_compressed_image = 2;
    string server_ip = 3;
    int32 ros_domain_id = 4;
    bool use_cosine_sdk = 5;
    string camera_model = 6;
}

message ModeSwitchRequest {
    Mode mode = 1;
    repeated ImagePreprocessParam img_preproc = 2;
    HybridDeployConfig hybrid_deploy_config = 3;
}

message ModeSwitchResponse {
    bool success = 1;
    string error_info = 2;
}