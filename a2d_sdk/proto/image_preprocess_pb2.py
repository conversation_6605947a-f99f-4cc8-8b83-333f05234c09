# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: image_preprocess.proto

from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='image_preprocess.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x16image_preprocess.proto\"G\n\tCropParam\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\t\n\x01l\x18\x02 \x01(\r\x12\t\n\x01t\x18\x03 \x01(\r\x12\t\n\x01r\x18\x04 \x01(\r\x12\t\n\x01\x62\x18\x05 \x01(\r\"S\n\x0bResizeParam\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\t\n\x01w\x18\x02 \x01(\r\x12\t\n\x01h\x18\x03 \x01(\r\x12\x1e\n\x06interp\x18\x04 \x01(\x0e\x32\x0e.Interpolation\"R\n\x10SwapChannelParam\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\x0e\n\x06order1\x18\x02 \x01(\r\x12\x0e\n\x06order2\x18\x03 \x01(\r\x12\x0e\n\x06order3\x18\x04 \x01(\r\"T\n\x15\x46isheyeUndistortParam\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\x0b\n\x03\x62\x61l\x18\x02 \x01(\x02\x12\x1e\n\x06interp\x18\x03 \x01(\x0e\x32\x0e.Interpolation\"\xde\x01\n\x14ImagePreprocessParam\x12\x16\n\x03pos\x18\x01 \x01(\x0e\x32\t.MountPos\x12)\n\tundistort\x18\x02 \x01(\x0b\x32\x16.FisheyeUndistortParam\x12\x18\n\x04\x63rop\x18\x03 \x01(\x0b\x32\n.CropParam\x12\x1c\n\x06resize\x18\x04 \x01(\x0b\x32\x0c.ResizeParam\x12\"\n\x07permute\x18\x05 \x01(\x0b\x32\x11.SwapChannelParam\x12\x11\n\tFrameRate\x18\x06 \x01(\x05\x12\x14\n\x0c\x64\x65pth_enable\x18\x07 \x01(\x08*\xd9\x01\n\x08MountPos\x12\x08\n\x04head\x10\x00\x12\r\n\thand_left\x10\x01\x12\x0e\n\nhand_right\x10\x02\x12\x15\n\x11\x62\x61\x63k_left_fisheye\x10\x03\x12\x15\n\x11hand_left_fisheye\x10\x04\x12\x16\n\x12hand_right_fisheye\x10\x05\x12\x16\n\x12\x62\x61\x63k_right_fisheye\x10\x06\x12\x17\n\x13head_center_fisheye\x10\x07\x12\x15\n\x11head_left_fisheye\x10\x08\x12\x16\n\x12head_right_fisheye\x10\t*#\n\nSensorType\x12\x08\n\x04RGBD\x10\x00\x12\x0b\n\x07\x46ISHEYE\x10\x01*3\n\rInterpolation\x12\x0b\n\x07NEAREST\x10\x00\x12\n\n\x06LINEAR\x10\x01\x12\t\n\x05\x43UBIC\x10\x02\x62\x06proto3'
)

_MOUNTPOS = _descriptor.EnumDescriptor(
  name='MountPos',
  full_name='MountPos',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='head', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='hand_left', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='hand_right', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='back_left_fisheye', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='hand_left_fisheye', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='hand_right_fisheye', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='back_right_fisheye', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='head_center_fisheye', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='head_left_fisheye', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='head_right_fisheye', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=580,
  serialized_end=797,
)
_sym_db.RegisterEnumDescriptor(_MOUNTPOS)

MountPos = enum_type_wrapper.EnumTypeWrapper(_MOUNTPOS)
_SENSORTYPE = _descriptor.EnumDescriptor(
  name='SensorType',
  full_name='SensorType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RGBD', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FISHEYE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=799,
  serialized_end=834,
)
_sym_db.RegisterEnumDescriptor(_SENSORTYPE)

SensorType = enum_type_wrapper.EnumTypeWrapper(_SENSORTYPE)
_INTERPOLATION = _descriptor.EnumDescriptor(
  name='Interpolation',
  full_name='Interpolation',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NEAREST', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LINEAR', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CUBIC', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=836,
  serialized_end=887,
)
_sym_db.RegisterEnumDescriptor(_INTERPOLATION)

Interpolation = enum_type_wrapper.EnumTypeWrapper(_INTERPOLATION)
head = 0
hand_left = 1
hand_right = 2
back_left_fisheye = 3
hand_left_fisheye = 4
hand_right_fisheye = 5
back_right_fisheye = 6
head_center_fisheye = 7
head_left_fisheye = 8
head_right_fisheye = 9
RGBD = 0
FISHEYE = 1
NEAREST = 0
LINEAR = 1
CUBIC = 2



_CROPPARAM = _descriptor.Descriptor(
  name='CropParam',
  full_name='CropParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enable', full_name='CropParam.enable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='l', full_name='CropParam.l', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='t', full_name='CropParam.t', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='r', full_name='CropParam.r', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='b', full_name='CropParam.b', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26,
  serialized_end=97,
)


_RESIZEPARAM = _descriptor.Descriptor(
  name='ResizeParam',
  full_name='ResizeParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enable', full_name='ResizeParam.enable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='w', full_name='ResizeParam.w', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='h', full_name='ResizeParam.h', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interp', full_name='ResizeParam.interp', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=99,
  serialized_end=182,
)


_SWAPCHANNELPARAM = _descriptor.Descriptor(
  name='SwapChannelParam',
  full_name='SwapChannelParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enable', full_name='SwapChannelParam.enable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='order1', full_name='SwapChannelParam.order1', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='order2', full_name='SwapChannelParam.order2', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='order3', full_name='SwapChannelParam.order3', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=184,
  serialized_end=266,
)


_FISHEYEUNDISTORTPARAM = _descriptor.Descriptor(
  name='FisheyeUndistortParam',
  full_name='FisheyeUndistortParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enable', full_name='FisheyeUndistortParam.enable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bal', full_name='FisheyeUndistortParam.bal', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interp', full_name='FisheyeUndistortParam.interp', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=268,
  serialized_end=352,
)


_IMAGEPREPROCESSPARAM = _descriptor.Descriptor(
  name='ImagePreprocessParam',
  full_name='ImagePreprocessParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pos', full_name='ImagePreprocessParam.pos', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='undistort', full_name='ImagePreprocessParam.undistort', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='ImagePreprocessParam.crop', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='resize', full_name='ImagePreprocessParam.resize', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='permute', full_name='ImagePreprocessParam.permute', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='FrameRate', full_name='ImagePreprocessParam.FrameRate', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='depth_enable', full_name='ImagePreprocessParam.depth_enable', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=355,
  serialized_end=577,
)

_RESIZEPARAM.fields_by_name['interp'].enum_type = _INTERPOLATION
_FISHEYEUNDISTORTPARAM.fields_by_name['interp'].enum_type = _INTERPOLATION
_IMAGEPREPROCESSPARAM.fields_by_name['pos'].enum_type = _MOUNTPOS
_IMAGEPREPROCESSPARAM.fields_by_name['undistort'].message_type = _FISHEYEUNDISTORTPARAM
_IMAGEPREPROCESSPARAM.fields_by_name['crop'].message_type = _CROPPARAM
_IMAGEPREPROCESSPARAM.fields_by_name['resize'].message_type = _RESIZEPARAM
_IMAGEPREPROCESSPARAM.fields_by_name['permute'].message_type = _SWAPCHANNELPARAM
DESCRIPTOR.message_types_by_name['CropParam'] = _CROPPARAM
DESCRIPTOR.message_types_by_name['ResizeParam'] = _RESIZEPARAM
DESCRIPTOR.message_types_by_name['SwapChannelParam'] = _SWAPCHANNELPARAM
DESCRIPTOR.message_types_by_name['FisheyeUndistortParam'] = _FISHEYEUNDISTORTPARAM
DESCRIPTOR.message_types_by_name['ImagePreprocessParam'] = _IMAGEPREPROCESSPARAM
DESCRIPTOR.enum_types_by_name['MountPos'] = _MOUNTPOS
DESCRIPTOR.enum_types_by_name['SensorType'] = _SENSORTYPE
DESCRIPTOR.enum_types_by_name['Interpolation'] = _INTERPOLATION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CropParam = _reflection.GeneratedProtocolMessageType('CropParam', (_message.Message,), {
  'DESCRIPTOR' : _CROPPARAM,
  '__module__' : 'image_preprocess_pb2'
  # @@protoc_insertion_point(class_scope:CropParam)
  })
_sym_db.RegisterMessage(CropParam)

ResizeParam = _reflection.GeneratedProtocolMessageType('ResizeParam', (_message.Message,), {
  'DESCRIPTOR' : _RESIZEPARAM,
  '__module__' : 'image_preprocess_pb2'
  # @@protoc_insertion_point(class_scope:ResizeParam)
  })
_sym_db.RegisterMessage(ResizeParam)

SwapChannelParam = _reflection.GeneratedProtocolMessageType('SwapChannelParam', (_message.Message,), {
  'DESCRIPTOR' : _SWAPCHANNELPARAM,
  '__module__' : 'image_preprocess_pb2'
  # @@protoc_insertion_point(class_scope:SwapChannelParam)
  })
_sym_db.RegisterMessage(SwapChannelParam)

FisheyeUndistortParam = _reflection.GeneratedProtocolMessageType('FisheyeUndistortParam', (_message.Message,), {
  'DESCRIPTOR' : _FISHEYEUNDISTORTPARAM,
  '__module__' : 'image_preprocess_pb2'
  # @@protoc_insertion_point(class_scope:FisheyeUndistortParam)
  })
_sym_db.RegisterMessage(FisheyeUndistortParam)

ImagePreprocessParam = _reflection.GeneratedProtocolMessageType('ImagePreprocessParam', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEPREPROCESSPARAM,
  '__module__' : 'image_preprocess_pb2'
  # @@protoc_insertion_point(class_scope:ImagePreprocessParam)
  })
_sym_db.RegisterMessage(ImagePreprocessParam)


# @@protoc_insertion_point(module_scope)
