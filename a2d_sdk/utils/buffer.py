import time
import copy

class GenericBuffer:
    def __init__(self, capacity=100, copy_func=None):
        """
        初始化通用 Buffer（环形缓冲区）。

        :param capacity: 缓冲区容量，最多存储的数据条数
        :param copy_func: 数据复制函数。默认为 copy.deepcopy，
                          确保写入和读取时均使用深度复制，防止数据被外部修改。
        """
        self.capacity = capacity
        self.buffer = [None] * capacity       # 存储数据
        self.timestamps = [None] * capacity     # 存储对应时间戳
        self.counter = 0                      # 写入计数器
        # 默认使用深度复制
        self.copy_func = copy_func if copy_func is not None else copy.deepcopy

    def write(self, data, timestamp=None):
        """
        写入一条数据及其时间戳到缓冲区（环形写入）

        :param data: 任意数据对象
        :param timestamp: 时间戳，默认为当前时间
        """
        if timestamp is None:
            timestamp = time.time()
        idx = self.counter % self.capacity
        # 使用深度复制存储数据，确保数据在写入后不会被外部修改影响
        self.buffer[idx] = self.copy_func(data)
        self.timestamps[idx] = timestamp
        self.counter += 1

    def read_range(self, t_min, t_max):
        """
        返回时间戳在 [t_min, t_max] 范围内的所有数据和对应时间戳

        :return: (list_of_data, list_of_timestamps)
        """
        valid_data = []
        valid_timestamps = []
        for i in range(self.capacity):
            ts = self.timestamps[i]
            if ts is not None and t_min <= ts <= t_max:
                valid_data.append(self.copy_func(self.buffer[i]))
                valid_timestamps.append(ts)
        return valid_data, valid_timestamps

    def read_nearest(self, t_target):
        """
        返回与目标时间戳 t_target 最接近的一条数据及其时间戳

        :return: (data, timestamp)；若无数据则返回 (None, None)
        """
        best_idx = None
        best_diff = float('inf')
        best_ts = None
        for i in range(self.capacity):
            ts = self.timestamps[i]
            if ts is None:
                continue
            diff = abs(ts - t_target)
            if diff < best_diff:
                best_diff = diff
                best_idx = i
                best_ts = ts
        if best_idx is not None:
            return self.copy_func(self.buffer[best_idx]), best_ts
        else:
            return None, None

    def read_latest(self):
        """
        返回最新写入的数据及其时间戳；如果没有写入数据，则返回 (None, None)
        """
        if self.counter == 0:
            return None, None
        idx = (self.counter - 1) % self.capacity
        return self.copy_func(self.buffer[idx]), self.timestamps[idx]


# ----------------- 示例测试代码 -----------------
if __name__ == '__main__':
    # 创建一个 GenericBuffer，容量为 5
    buffer = GenericBuffer(capacity=5)

    # 模拟写入 5 条数据，每条数据为一个整数，同时记录写入时间戳
    data_infos = []
    for i in range(5):
        time.sleep(0.05)  # 保证时间戳不同
        ts = time.time()
        data = i  # 这里可以替换成任意数据类型
        buffer.write(data, timestamp=ts)
        data_infos.append((ts, i))
        print(f"写入数据 {i}, 时间戳: {ts}")

    # 测试 read_range：选择第2到第4条数据的时间范围
    t_min = data_infos[1][0]
    t_max = data_infos[3][0]
    datas_range, ts_range = buffer.read_range(t_min, t_max)
    print("\nread_range 返回结果:")
    for d, ts in zip(datas_range, ts_range):
        print(f"数据: {d}, 时间戳: {ts}")

    # 测试 read_nearest：以第3条数据的时间戳作为目标
    t_target = data_infos[2][0]
    data_nearest, ts_nearest = buffer.read_nearest(t_target)
    print("\nread_nearest 返回结果:")
    print(f"目标时间戳: {t_target}, 返回的数据: {data_nearest}, 时间戳: {ts_nearest}")

    # 测试 read_latest：获取最新写入的数据
    data_latest, ts_latest = buffer.read_latest()
    print("\nread_latest 返回结果:")
    print(f"最新数据: {data_latest}, 时间戳: {ts_latest}")
