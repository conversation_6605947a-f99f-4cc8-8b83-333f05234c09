#!/bin/bash

# 设置工程目录为脚本所在目录的上一级
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR"

# 检查 aorta-service 是否运行
if ! pgrep -f "aorta service" > /dev/null; then
    echo "启动 aorta service..."
    "${PROJECT_DIR}/bin/aorta" service -g -d \
        --listen-peer-urls 'http://10.42.0.101:2380' \
        --listen-client-urls 'http://10.42.0.101:2379' &
    sleep 2  # 等待服务启动
else
    echo "aorta service 已在运行"
fi

source "${PROJECT_DIR}/env.sh"

# 检查并停止 a2d_app 服务
if systemctl is-active --quiet a2d_app; then
    echo "停止 a2d_app 服务..."
    sudo systemctl stop a2d_app
    sleep 1
fi

# 检查并终止 cosine_runner 进程
COSINE_PID=$(pgrep -f "cosine_runner")
if [ ! -z "$COSINE_PID" ]; then
    echo "终止现有的 cosine_runner 进程..."
    kill -9 $COSINE_PID
    sleep 1
fi

# 启动新的 cosine_runner
echo "启动新的 cosine_runner..."
"${PROJECT_DIR}/bin/cosine_runner" . develop &

echo "启动流程完成"
