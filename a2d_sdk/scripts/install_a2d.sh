#!/bin/bash

# check if the user is root
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root"
  exit 1
fi

CURRENT_SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)

# check if the service is already installed
if systemctl is-active --quiet a2d_mode_switch_server; then
  echo "Service is already installed, disable it first..."
  systemctl stop a2d_mode_switch_server
  systemctl disable a2d_mode_switch_server
fi

cat << EOF | sudo tee /etc/systemd/system/a2d_mode_switch_server.service > /dev/null
[Unit]
Description=a2d_mode_switch_server
After=a2d_nvme.service

[Service]
ExecStart=/bin/bash $CURRENT_SCRIPT_DIR/scripts/start_mode_switch_server.sh
WorkingDirectory=$CURRENT_SCRIPT_DIR
Restart=always
User=$USER
Group=$USER
Environment=PATH=/usr/bin:/usr/local/bin
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd 配置
systemctl daemon-reload

# 启动并设置为开机自启
systemctl start a2d_mode_switch_server
systemctl enable a2d_mode_switch_server

# check if the service is running
systemctl status a2d_mode_switch_server
