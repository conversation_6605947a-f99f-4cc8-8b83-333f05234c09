import zmq
import os
import sys
import subprocess
from enum import Enum
import subprocess
import logging
import time
import datetime
from google.protobuf import text_format
import json

current_dir = os.path.dirname(os.path.abspath(__file__))
workspace_dir = os.path.realpath(os.path.join(current_dir, "../"))
print(f"workspace_dir: {workspace_dir}")
sys.path.append(workspace_dir)
sys.path.append(os.path.join(workspace_dir, "proto"))

from proto.mode_switch_pb2 import ModeSwitchRequest, Mode, ModeSwitchResponse
from proto.image_preprocess_pb2 import ImagePreprocessParam

ros_setup_path = "/opt/ros/humble/setup.bash"
mode_switch_request_path = os.path.join("/data/logs/sdk/", "mode_switch_request.txt")
cosine_sdk_path = os.path.join(workspace_dir, "scripts/start_a2d_cosine.sh")
cosine_sdk_type = ""

def setup_logger(logger_name):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)
    # integrate with other projects, write log to parent directory
    log_dir = os.path.join("/data/logs/", "sdk")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    file_handler = logging.FileHandler(f"{log_dir}/{logger_name}.txt")
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.ERROR)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger

server_logger = setup_logger("a2d_mode_switch")

class HybridDeployConfig:
    def __init__(self,
                use_zmq=True,
                use_ros_compressed_image=False,
                server_ip=None,
                ros_domain_id="",
                mode_switch_config=None):
        self.use_zmq = use_zmq
        self.use_ros_compressed_image = use_ros_compressed_image
        self.server_ip = server_ip
        self.ros_domain_id = ros_domain_id
        self.mode_switch_config = mode_switch_config

def clear_archive_log():
    log_dir = os.path.join("/data/logs/", "sdk")
    clear_cmd = f"mkdir -p {log_dir} && rm -rf {log_dir}/*.tar.gz"
    try:
        subprocess.run(clear_cmd, shell=True, check=True)
    except Exception as e:
        server_logger.error(f"Failed to clear log: {e}")

def archieve_log():
    log_dir = os.path.join("/data/logs/", "sdk")
    timestamp = datetime.datetime.now().strftime("%Y%m%d.%H%M%S")
    log_file_name = f"log_{timestamp}.tar.gz"
    log_file_path = os.path.join(log_dir, log_file_name)
    cmd = f"cd /data/logs/ && tar --warning=no-file-changed --exclude='*.tar.gz' -zcvf {log_file_name} sdk/ && mv {log_file_name} sdk/"
    clear_cmd_txt = f"cd /data/logs/sdk && ls *.txt | grep -v a2d_mode_switch | xargs rm -rf"
    clear_cmd_log = f"cd /data/logs/sdk && ls *INFO* | grep -v a2d_mode_switch | xargs rm -rf"
    try:
        subprocess.run(cmd, shell=True, check=True)
        subprocess.run(clear_cmd_txt, shell=True, check=True)
        subprocess.run(clear_cmd_log, shell=True, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Warning: Command failed with error {e}")

def start_http_server():
    # check if http server is already running by checking process name
    ps_command = f"ps aux | grep http.server | grep -v grep"
    try:
        result = subprocess.check_output(ps_command, shell=True, text=True)
        if "http.server" in result:
            server_logger.info("http server is already running.")
            return True
    except subprocess.CalledProcessError:
        pass

    server_logger.info("Starting http server...")
    subprocess.Popen(f"nohup bash {workspace_dir}/scripts/start_http_server.sh > /data/logs/sdk/http_server.txt 2>&1 &", shell=True)
    server_logger.info("http server started successfully.")
    return True

def stop_recorder_system():
    # 停止 a2d_app
    server_logger.info("Stopping a2d_app...")
    subprocess.run("systemctl stop a2d_app", shell=True, check=True)
    server_logger.info("a2d_app stopped successfully.")
    server_logger.info("Stopping perf_guard ...")
    subprocess.run("systemctl stop agibot_perfguard", shell=True, check=True)
    server_logger.info("perf_guard stopped successfully.")
    return True

def start_recorder_system():
    # 启动 a2d_app
    server_logger.info("Starting a2d_app...")
    subprocess.run("systemctl start a2d_app", shell=True, check=True)
    server_logger.info("a2d_app started successfully.")
    server_logger.info("Starting perf_guard ...")
    subprocess.run("systemctl start agibot_perfguard", shell=True, check=True)
    server_logger.info("perf_guard started successfully.")
    return True

# 增加enum映射
MountPos = {
    0: "head",
    1: "hand_left",
    2: "hand_right",
    3: "back_left_fisheye",
    4: "hand_left_fisheye",
    5: "hand_right_fisheye",
    6: "back_right_fisheye",
    7: "head_center_fisheye",
    8: "head_left_fisheye",
    9: "head_right_fisheye"
}

# 添加相机配置相关的常量
CAMERA_NAME_TO_ID = {
    "head": {"camera_id": "d455_1", "width": 1280, "height": 720},
    "hand_left": {"camera_id": "d405_1", "width": 848, "height": 480},
    "hand_right": {"camera_id": "d405_2", "width": 848, "height": 480},
    "back_left_fisheye": {"camera_id": "cam0", "width": 1920, "height": 1536, "calibFile": "/data/parameters/back_left_fisheye_intrinsic_params.json"},
    "hand_left_fisheye": {"camera_id": "cam1", "width": 1920, "height": 1536, "calibFile": "/data/parameters/hand_left_fisheye_intrinsic_params.json"},
    "hand_right_fisheye": {"camera_id": "cam2", "width": 1920, "height": 1536, "calibFile": "/data/parameters/hand_right_fisheye_intrinsic_params.json"},
    "back_right_fisheye": {"camera_id": "cam3", "width": 1920, "height": 1536, "calibFile": "/data/parameters/back_right_fisheye_intrinsic_params.json"},
    "head_center_fisheye": {"camera_id": "cam4", "width": 1920, "height": 1536, "calibFile": "/data/parameters/head_center_fisheye_intrinsic_params.json"},
    "head_left_fisheye": {"camera_id": "cam5", "width": 1920, "height": 1536, "calibFile": "/data/parameters/head_left_fisheye_intrinsic_params.json"},
    "head_right_fisheye": {"camera_id": "cam6", "width": 1920, "height": 1536, "calibFile": "/data/parameters/head_right_fisheye_intrinsic_params.json"}
}

def analyze_camera_config(config: ModeSwitchRequest):
    if len(config.img_preproc) == 0:
        server_logger.error("No camera config found.Will use default config.")
        # 检查文件是否存在，存在则删除
        fisheye_conf = os.path.join(workspace_dir, "/home/<USER>/app/conf/deploy/develop/fisheye_camera_conf.json")
        rs_conf = os.path.join(workspace_dir, "/home/<USER>/app/conf/deploy/develop/rs_camera_conf.json")
        
        if os.path.exists(fisheye_conf):
            os.remove(fisheye_conf)
        if os.path.exists(rs_conf):
            os.remove(rs_conf)
        return True
    
    rs_camera_config = {}
    fisheye_camera_config = {}
    for img_preproc in config.img_preproc:
        camera_pos = img_preproc.pos
        camera_name = MountPos[camera_pos]
        if camera_name in CAMERA_NAME_TO_ID:
            camera_id = CAMERA_NAME_TO_ID[camera_name]["camera_id"]
            width = CAMERA_NAME_TO_ID[camera_name]["width"]
            height = CAMERA_NAME_TO_ID[camera_name]["height"]
            if 'fisheye' in camera_name:
                fisheye_camera_config[camera_id] = {}
                fisheye_camera_config[camera_id]["name"] = camera_name
                fisheye_camera_config[camera_id]["width"]       = str(width)
                fisheye_camera_config[camera_id]["height"]      = str(height)
                # 检查是否存在FrameRate
                if hasattr(img_preproc, "FrameRate"):
                    if img_preproc.FrameRate == 5 or img_preproc.FrameRate == 10 or img_preproc.FrameRate == 15 or img_preproc.FrameRate == 20 or img_preproc.FrameRate == 25 or img_preproc.FrameRate == 30:
                        fisheye_camera_config[camera_id]["fps"] = str(img_preproc.FrameRate)
                    else:
                        fisheye_camera_config[camera_id]["fps"] = str(30)
                else:
                    fisheye_camera_config[camera_id]["fps"] = str(30)
                fisheye_camera_config[camera_id]["img_process"] = {}
                fisheye_camera_config[camera_id]["img_process"]["enabled"] = False
                if hasattr(img_preproc, "crop") and img_preproc.crop.enable:
                    fisheye_camera_config[camera_id]["img_process"]["enabled"] = True
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"] = {}
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["left"]   = str(img_preproc.crop.l)
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["top"]    = str(img_preproc.crop.t)
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["right"]  = str(img_preproc.crop.r)
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["bottom"] = str(img_preproc.crop.b)
                if hasattr(img_preproc, "resize") and img_preproc.resize.enable:
                    if "crop_resize" not in fisheye_camera_config[camera_id]["img_process"]:
                        fisheye_camera_config[camera_id]["img_process"]["crop_resize"] = {}
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["resizewidth"]  = str(img_preproc.resize.w)
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["resizeheight"] = str(img_preproc.resize.h)
                elif hasattr(img_preproc, "crop") and img_preproc.crop.enable:
                    if "crop_resize" not in fisheye_camera_config[camera_id]["img_process"]:
                        fisheye_camera_config[camera_id]["img_process"]["crop_resize"] = {}
                    # 如果crop没有设置，则根据width和height计算crop
                    width_re = (int(img_preproc.crop.r) - int(img_preproc.crop.l))
                    height_re = (int(img_preproc.crop.b) - int(img_preproc.crop.t))
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["resizewidth"]  = str(width_re)
                    fisheye_camera_config[camera_id]["img_process"]["crop_resize"]["resizeheight"] = str(height_re)
                if hasattr(img_preproc, "undistort") and img_preproc.undistort.enable:
                    fisheye_camera_config[camera_id]["img_process"]["enabled"] = True
                    fisheye_camera_config[camera_id]["img_process"]["balance"] = str(img_preproc.undistort.bal)
                else:
                    fisheye_camera_config[camera_id]["img_process"]["balance"] = "1.0"
                if hasattr(img_preproc, "permute") and img_preproc.permute.enable:
                    fisheye_camera_config[camera_id]["img_process"]["enabled"] = True
                    list_order = [img_preproc.permute.order1, img_preproc.permute.order2, img_preproc.permute.order3]
                    fisheye_camera_config[camera_id]["img_process"]["permuteOrder"] = list_order
                else:
                    fisheye_camera_config[camera_id]["img_process"]["enabled"] = True
                    fisheye_camera_config[camera_id]["img_process"]["permuteOrder"] = [0, 1, 2]
            else:
                rs_camera_config[camera_id] = {}
                rs_camera_config[camera_id]["name"] = camera_name
                rs_camera_config[camera_id]["width"]       = str(width)
                rs_camera_config[camera_id]["height"]      = str(height)
                # 检查是否存在FrameRate
                if hasattr(img_preproc, "FrameRate"):
                    if img_preproc.FrameRate == 5 or img_preproc.FrameRate == 10 or img_preproc.FrameRate == 15 or img_preproc.FrameRate == 20 or img_preproc.FrameRate == 25 or img_preproc.FrameRate == 30:
                        rs_camera_config[camera_id]["fps"] = str(img_preproc.FrameRate)
                    else:
                        rs_camera_config[camera_id]["fps"] = str(30)
                else:
                    rs_camera_config[camera_id]["fps"] = str(30)
                if hasattr(img_preproc, "depth_enable"):
                    rs_camera_config[camera_id]["depthEnable"] = img_preproc.depth_enable
                else:
                    rs_camera_config[camera_id]["depthEnable"] = False
                rs_camera_config[camera_id]["img_process"] = {}
                rs_camera_config[camera_id]["img_process"]["enabled"] = False
                if hasattr(img_preproc, "crop") and img_preproc.crop.enable:
                    rs_camera_config[camera_id]["img_process"]["crop_resize"] = {}
                    rs_camera_config[camera_id]["img_process"]["enabled"] = True
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["left"]   = str(img_preproc.crop.l)
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["top"]    = str(img_preproc.crop.t)
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["right"]  = str(img_preproc.crop.r)
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["bottom"] = str(img_preproc.crop.b)
                if hasattr(img_preproc, "resize") and img_preproc.resize.enable:
                    if "crop_resize" not in rs_camera_config[camera_id]["img_process"]:
                        rs_camera_config[camera_id]["img_process"]["crop_resize"] = {}
                    rs_camera_config[camera_id]["img_process"]["enabled"] = True
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["resizewidth"]  = str(img_preproc.resize.w)
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["resizeheight"] = str(img_preproc.resize.h)
                elif hasattr(img_preproc, "crop") and img_preproc.crop.enable:
                    if "crop_resize" not in rs_camera_config[camera_id]["img_process"]:
                        rs_camera_config[camera_id]["img_process"]["crop_resize"] = {}
                    # 如果crop没有设置，则根据width和height计算crop
                    width_re = (int(img_preproc.crop.r) - int(img_preproc.crop.l))
                    height_re = (int(img_preproc.crop.b) - int(img_preproc.crop.t))
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["resizewidth"]  = str(width_re)
                    rs_camera_config[camera_id]["img_process"]["crop_resize"]["resizeheight"] = str(height_re)
                if hasattr(img_preproc, "permute") and img_preproc.permute.enable:
                    rs_camera_config[camera_id]["img_process"]["enabled"] = True
                    list_order = [img_preproc.permute.order1, img_preproc.permute.order2, img_preproc.permute.order3]
                    rs_camera_config[camera_id]["img_process"]["permuteOrder"] = list_order 
                else:
                    rs_camera_config[camera_id]["img_process"]["permuteOrder"] = [0, 1, 2]
        else:
            server_logger.error(f"Unknown camera position: {camera_pos}")
            return False
    
    # 将配置写入文件
    # 检查配置不为空则写入
    if len(fisheye_camera_config) > 0:
        with open(os.path.join(workspace_dir, "/home/<USER>/app/conf/deploy/develop/fisheye_camera_conf.json"), "w") as f:
            json.dump(fisheye_camera_config, f)
    if len(rs_camera_config) > 0:
        with open(os.path.join(workspace_dir, "/home/<USER>/app/conf/deploy/develop/rs_camera_conf.json"), "w") as f:
            json.dump(rs_camera_config, f)
    return True

def start_launcher(config: ModeSwitchRequest):
    # 后台启动 camera_sender.py
    args = f" -p {mode_switch_request_path}"
    is_use_cosine_sdk = config.hybrid_deploy_config.use_cosine_sdk
    camera_model = config.hybrid_deploy_config.camera_model
    if is_use_cosine_sdk:
        if camera_model == "camera_depth_53":
            cmd = f"{cosine_sdk_path} start camera_depth_53"
            cosine_sdk_type = "camera_depth_53"
        elif camera_model == "camera_fisheye_71":
            cmd = f"{cosine_sdk_path} start camera_fisheye_71"
            cosine_sdk_type = "camera_fisheye_71"
        elif camera_model == "camera_preprocess_71":
            cmd = f"{cosine_sdk_path} start camera_preprocess_71"
            cosine_sdk_type = "camera_preprocess_71"
        elif camera_model == "develop":
            analyze_camera_config(config)
            cmd = f"{cosine_sdk_path} start develop"
            cosine_sdk_type = "develop"
        else:
            server_logger.error(f"Unknown camera model: {camera_model}")
            return False
        server_logger.info(f"Starting start_a2d_cosine.sh in the background: {cmd}")
        subprocess.Popen(
            f"nohup bash -c '{cmd}' > /data/logs/sdk/hybrid_camera.txt 2>&1 &", shell=True
        )
        server_logger.info("start_a2d_cosine.sh started successfully.")
    else:
        server_logger.info("Not using cosine sdk, skip starting start_a2d_cosine.sh")
    
    server_logger.info("launcher started successfully.")
    return True

def kill_process(process_keyword):
    try:
        ps_command = f"ps aux | grep {process_keyword}" + " | grep -v grep | awk '{print $2}'"
        result = subprocess.check_output(ps_command, shell=True, text=True)

        pids = result.strip().split()
        if pids:
            server_logger.info(f"Found PIDs: {pids}, killing...")
            kill_command = f"kill -9 {' '.join(pids)}"
            subprocess.run(kill_command, shell=True, check=True)
            server_logger.info(f"{process_keyword} processes killed successfully.")
        else:
            server_logger.info(f"No {process_keyword} processes found.")
        return True
    except subprocess.CalledProcessError as e:
        server_logger.error(f"Error executing command: {e}")
        return False
    except Exception as e:
        server_logger.error(f"Unexpected error: {e}")
        return False

def stop_hybrid_camera():
    return kill_process("cosine_runner")

def stop_hybrid_hal():
    return kill_process("hal")

def stop_remote_hal():
    return kill_process("remote_hal")

def stop_vr_control():
    return kill_process("vr_control")

def stop_mc_adapter():
    return kill_process("retarget_trans.py")

def stop_mc_app():
    return kill_process("genie_motion_control")

def stop_launcher():
    cmd = f"{cosine_sdk_path} stop {cosine_sdk_type}"
    server_logger.info(f"Stopping launcher with {cmd} in the background...")
    subprocess.Popen(f"nohup {cmd} > /data/logs/sdk/launcher.txt 2>&1 &", shell=True)
    server_logger.info("launcher stopped successfully.")
    return True

def save_mode_witch_request(config: ModeSwitchRequest):
    with open(mode_switch_request_path, "w") as f:
        f.write(text_format.MessageToString(config))

def start_hybrid_system(config: ModeSwitchRequest):
    return start_launcher(config)

def stop_hybrid_system():
    #kill twice
    return stop_launcher() and stop_hybrid_camera() and stop_hybrid_hal() and stop_vr_control() and stop_mc_adapter() and stop_mc_app() and stop_remote_hal()


class FunctionMode(Enum):
    ERROR = 0
    IDLE= 1
    HYBRID = 2
    RECORDER = 3

class ModeSwitchServer:
    def __init__(self):
        self.function_mode = FunctionMode.RECORDER

    def set_function_mode(self, function_mode):
        self.function_mode = function_mode

    def get_function_mode(self):
        return self.function_mode
    
    def switch_to_idle(self):
        server_logger.info(f"Switching to idle mode, current function mode: {self.function_mode}")
        if self.function_mode == FunctionMode.IDLE:
            return True
        ret = stop_hybrid_system() and stop_recorder_system()
        if ret:
            self.function_mode = FunctionMode.IDLE
        else:
            self.function_mode = FunctionMode.ERROR
        return ret

    def switch_to_hybrid(self, config: ModeSwitchRequest):
        """
        停止 a2d_app 服务并后台启动 camera_sender.py
        """
        server_logger.info(f"Switching to hybrid mode, current function mode: {self.function_mode}")
        #先切换到IDLE模式
        if not self.switch_to_idle():
            return False
        #再切换到hybrid模式
        if self.function_mode == FunctionMode.HYBRID:
            return True
        if self.function_mode == FunctionMode.ERROR:
            return False

        try:
            if stop_hybrid_system() and stop_recorder_system() and start_hybrid_system(config):
                self.function_mode = FunctionMode.HYBRID
                return True
            else:
                self.function_mode = FunctionMode.ERROR
                return False
        except subprocess.CalledProcessError as e:
            self.function_mode = FunctionMode.ERROR
            server_logger.error(f"Error: {e}")
            return False
    
    def switch_to_recorder(self):
        server_logger.info(f"Switching to recorder mode, current function mode: {self.function_mode}")
  	#先切换到IDLE模式
        if not self.switch_to_idle():
            return False
        #再切换到hybrid模式
        if self.function_mode == FunctionMode.RECORDER:
            return True
        if self.function_mode == FunctionMode.ERROR:
            return False

        try:
            if stop_hybrid_system() and start_recorder_system():
                self.function_mode = FunctionMode.RECORDER
                return True
            else:
                self.function_mode = FunctionMode.ERROR
                return False
        except Exception as e:
            self.function_mode = FunctionMode.ERROR
            server_logger.error(f"Error: {e}")
            return False

def server():
    clear_archive_log()

    os.makedirs(os.path.join("/data/logs/", "sdk"), exist_ok=True)
    start_http_server()

    context = zmq.Context()
    socket = context.socket(zmq.REP)
    socket.bind("tcp://10.42.0.101:8848")

    mode_switch_server = ModeSwitchServer()

    server_logger.info("Server is running and waiting for requests...")

    while True:
        message = socket.recv_string()
        archieve_log()

        config = ModeSwitchRequest()
        text_format.Merge(message, config)
        save_mode_witch_request(config)

        if config.mode == Mode.HYBRID_DEPLOY:
            if mode_switch_server.switch_to_hybrid(config):
                response = ModeSwitchResponse()
                response.success = True
                socket.send_string(text_format.MessageToString(response))
            else:
                response = ModeSwitchResponse()
                response.success = False
                response.error_info = "Switch to 'hybrid' failed."
                socket.send_string(text_format.MessageToString(response))
        elif config.mode == Mode.DATA_COLLECTION:
            if mode_switch_server.switch_to_recorder():
                response = ModeSwitchResponse()
                response.success = True
                socket.send_string(text_format.MessageToString(response))
            else:
                response = ModeSwitchResponse()
                response.success = False
                response.error_info = "Switch to 'recorder' failed."
                socket.send_string(text_format.MessageToString(response))
        elif config.mode == Mode.IDLE:
            if mode_switch_server.switch_to_idle():
                response = ModeSwitchResponse()
                response.success = True
                socket.send_string(text_format.MessageToString(response))
            else:
                response = ModeSwitchResponse()
                response.success = False
                response.error_info = "Switch to 'idle' failed."
                socket.send_string(text_format.MessageToString(response))
        else:
            response = ModeSwitchResponse()
            response.success = False
            response.error_info = "Unknown mode request."
            socket.send_string(text_format.MessageToString(response))

if __name__ == "__main__":
    server()
