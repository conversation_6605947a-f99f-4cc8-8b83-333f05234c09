#!/bin/bash

action_value=$1
action_param=$2

SCRIPTPATH=/home/<USER>/app
HOMEPATH=$SCRIPTPATH
CONFPATH=$SCRIPTPATH/a2d_sdk/conf
if [ ! -d $SCRIPTPATH ]; then
    >&2 echo "[ERROR] app path not exists"
    exit 1
fi

export CB_PROC_THREADS_NUM=1
export DYLOG_ASYNC_INTERVAL=5000
export GLOG_log_dir=/data/logs/sdk/
export GLOG_max_log_size=20
/usr/bin/mkdir -p $GLOG_log_dir
/usr/bin/chown -R agi:agi $GLOG_log_dir

export XCONN_DISCOVERY_URI=http://***********:2379 
export LOCATOR_IP=***********

cd $SCRIPTPATH
source $SCRIPTPATH/env.sh $SCRIPTPATH
export LD_LIBRARY_PATH="$LD_LIBRARY_PATH:$SCRIPTPATH/lib/hal"

if [ "$action_param" == "camera_depth_53" ]; then
    launcher_config="${CONFPATH}/launcher/53_depth.json"
elif [ "$action_param" == "camera_fisheye_71" ]; then
    launcher_config="${CONFPATH}/launcher/71.json"
elif [ "$action_param" == "camera_preprocess_71" ]; then
    launcher_config="${CONFPATH}/launcher/71_preprocess.json"
elif [ "$action_param" == "develop" ]; then
    launcher_config="${CONFPATH}/launcher/develop.json"
else
    echo "unknown action param: $action_param"
    exit 1
fi

if [ "$action_value" == "start" ]; then
    killall -15 launcher
    $HOMEPATH/bin/launcher -m $launcher_config
    systemctl stop agibot_perfguard
elif [ "$action_value" == "stop" ]; then
    killall -15 launcher
    $HOMEPATH/bin/launcher --kill $launcher_config
    systemctl start agibot_perfguard
else
    >&2 echo "unknown action type: $action_value"
fi

