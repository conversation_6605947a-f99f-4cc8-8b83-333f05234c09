if [ $# -ne 1 ]; then
    echo "Usage: $0 <a2d_sdk_tarball_url>"
    exit 1
fi

a2d_sdk_tarball_url=$1

current_dir=$(pwd)
# CURRENT_SCRIPT_DIR="$(dirname "$(realpath "${BASH_SOURCE[0]}")")"

mkdir -p $current_dir/temp
rm -rf $current_dir/temp/*
cd $current_dir/temp

mkdir -p deps/zmq
cd deps/zmq

zmq_install_wheel="pyzmq-26.2.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"

wget https://file.agibot.com/a2d_sdk/deps/zmq/$zmq_install_wheel -O $zmq_install_wheel
wget https://file.agibot.com/a2d_sdk/deps/zmq/md5 -O pyzmq.md5

file_md5=`md5sum $zmq_install_wheel | awk '{print $1}'`
echo "file_md5: $file_md5"
check_md5=`cat pyzmq.md5 | awk '{print $1}'`
echo "check_md5: $check_md5"

if [ "$file_md5" != "$check_md5" ]; then
    echo "pyzmq wheel md5 check failed"
    exit 1
fi

cd $current_dir/temp
echo "downloading a2d sdk tarball"
# echo "a2d_sdk_tarball_url: $a2d_sdk_tarball_url" > a2d_sdk_tarball_url.txt
wget $a2d_sdk_tarball_url -O a2d_sdk.tar.gz
wget $a2d_sdk_tarball_url.md5 -O a2d_sdk.md5

file_md5=`md5sum a2d_sdk.tar.gz | awk '{print $1}'`
echo "file_md5: $file_md5"
check_md5=`cat a2d_sdk.md5 | awk '{print $1}'`
echo "check_md5: $check_md5"

if [ "$file_md5" != "$check_md5" ]; then
    echo "a2d sdk tarball md5 check failed"
    exit 1
fi

tar -zxvf a2d_sdk.tar.gz
mv deps app/

# write a shell script to install the a2d_sdk
cat <<EOF > ota_install_a2d_sdk.sh
#! /bin/bash
CURRENT_SCRIPT_DIR="\$(dirname "\$(realpath "\${BASH_SOURCE[0]}")")"

tar zxvf \$CURRENT_SCRIPT_DIR/a2d_sdk.tar.gz -C /home/<USER>
pip3 install /home/<USER>/app/deps/zmq/$zmq_install_wheel


bash /home/<USER>/app/a2d_sdk/install_a2d.sh

EOF

chmod +x ota_install_a2d_sdk.sh
tar -zcvf a2d_sdk.tar.gz app
tar -zcvf ota_install_a2d_sdk.tar.gz ota_install_a2d_sdk.sh a2d_sdk.tar.gz
if [ -f $current_dir/ota_install_a2d_sdk.tar.gz ]; then
    rm -rf $current_dir/ota_install_a2d_sdk.tar.gz
fi
mv ota_install_a2d_sdk.tar.gz $current_dir/
rm -rf $current_dir/temp
