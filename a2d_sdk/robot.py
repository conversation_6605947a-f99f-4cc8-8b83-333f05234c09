#! /usr/bin/env python3
# -*- coding: utf-8 -*-
import threading
import numpy as np
from cosine_bus.agibotdds_py3 import agibotdds

from std_msgs_pb.msg import Header_pb2 as HeaderPb
from sensor_msgs_pb.msg.JointState_pb2 import JointState
from geometry_msgs_pb.msg.TwistStamped_pb2 import TwistStamped
from geometry_msgs_pb.msg.Pose_pb2 import Pose
from genie_msgs_pb.msg.WaistState_pb2 import WaistState
from genie_msgs_pb.msg.ArmState_pb2 import ArmState
from genie_msgs_pb.msg.ModelPredict_pb2 import ModelPredict
from genie_msgs_pb.srv.BodyPose_pb2 import BodyPose_Request as BodyPoseRequest, BodyPose_Response as BodyPoseResponse
from genie_msgs_pb.msg.HeadState_pb2 import HeadState

import enum
import numpy as np
import time
import sys
import os
import ruckig

current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(current_dir))

from a2d_sdk.utils.sdk_logger import hal_logger
from a2d_sdk.utils.buffer import GenericBuffer
from a2d_sdk.camera.camera_receiver_cosine import CameraReceiverCosine

from scipy.spatial.transform import Rotation as R

class CosineCamera:
    def __init__(self, camera_group: list[str]):
        self.camera_group = camera_group
        self.receiver = CameraReceiverCosine(camera_group)

    def get_latest_image(self, camera_name: str) -> np.ndarray:
        return self.receiver.get_latest_image(camera_name)

    def get_latest_packet(self, camera_name: str):
        return self.receiver.get_latest_packet(camera_name)

    def get_fps(self, camera_name: str):
        return self.receiver.get_fps(camera_name)

    def get_latency_stats(self, camera_name: str, window_seconds: float = 5.0):
        return self.receiver.get_latency_stats(camera_name, window_seconds)

    def get_image_nearest(self, camera_name: str, timestamp_ns: int) -> np.ndarray:
        """获取指定时间戳最近的图像
        
        Args:
            camera_name: 相机名称
            timestamp_ns: 时间戳(纳秒)
            
        Returns:
            np.ndarray: 图像数据,如果没有收到有效数据则返回None
        """
        return self.receiver.get_image_nearest(camera_name, timestamp_ns)

    def get_packet_nearest(self, camera_name: str, timestamp_ns: int):
        """获取指定时间戳最近的图像包
        
        Args:
            camera_name: 相机名称
            timestamp_ns: 时间戳(纳秒)
            
        Returns:
            cosine_bus.Packet: 图像包,如果没有收到有效数据则返回None
        """
        return self.receiver.get_packet_nearest(camera_name, timestamp_ns)

    def close(self):
        self.receiver.close()

class Robot:
    def __init__(self):
        self.waist_initial_joint_position = [0.523, 0.279]
        self.head_initial_joint_position = [0.000, 0.506]
        self.arm_initial_joint_position = [
            0.881, 0.671, -0.5369, -1.8702, 0.8555, 0.6907, 1.1398,
            -0.881, -0.671, 0.5369, 1.8702, -0.8555, -0.6907, -1.1398
        ]
        self.hand_initial_joint_position = [
            0.881, 0.671, -0.5369, -1.8702, 0.8555, 0.6907, 
            -0.881, -0.671, 0.5369, 1.8702, -0.8555, -0.6907,
        ]

        self.arm_joint_names = [
            'Joint1_l', 'Joint2_l', 'Joint3_l', 'Joint4_l', 'Joint5_l', 'Joint6_l', 'Joint7_l',
            'Joint1_r', 'Joint2_r', 'Joint3_r', 'Joint4_r', 'Joint5_r', 'Joint6_r', 'Joint7_r'
        ]
        self.hand_joint_names = [
            # Left
            'left_thumb_flex', 'left_index_flex', 'left_middle_flex',
            'left_ring_flex', 'left_little_flex', 'left_thumb_rot',
            # Right
            'right_thumb_flex', 'right_index_flex', 'right_middle_flex',
            'right_ring_flex', 'right_little_flex', 'right_thumb_rot'
        ]
    
    def body_pose_joint_states(self):
        raise NotImplementedError("Body joint states not implemented")
    
    def head_joint_states(self):
        raise NotImplementedError("Head joint states not implemented")

    def waist_joint_states(self):
        raise NotImplementedError("Waist joint states not implemented")

    def arm_joint_states(self):
        raise NotImplementedError("Arm joint states not implemented")

    def gripper_states(self):
        raise NotImplementedError("Gripper joint states not implemented")

    def hand_joint_states(self):
        raise NotImplementedError("Hand joint states not implemented")

    def hand_force_states(self):
        raise NotImplementedError("Hand force states not implemented")

    def move_arm(self, positions):
        raise NotImplementedError("Move arm not implemented")

    def move_gripper(self, positions):
        raise NotImplementedError("Move gripper not implemented")

    def move_hand(self, positions):
        raise NotImplementedError("Move hand not implemented")
    
    def move_waist(self, positions):
        raise NotImplementedError("Move waist not implemented")

    def move_head(self, positions):
        raise NotImplementedError("Move head not implemented")
    
    def move_wheel(self, linear, angular):
        raise NotImplementedError("Move wheel not implemented")

    def reset(self, arm_positions=None, gripper_positions=None, hand_positions=None, waist_positions=None, head_positions=None):
        raise NotImplementedError("Reset not implemented")

class RobotDds(Robot):
    def __init__(self,
                 arm_topic='/hal/arm_joint_state',
                 arm_cmd_topic='/wbc/arm_command',
                 left_arm_force_topic='/hal/left_arm_data',
                 right_arm_force_topic='/hal/right_arm_data',
                 gripper_topic='/hal/gripper_joint_state',
                 gripper_cmd_topic='/wbc/gripper_command',
                 hand_cmd_topic='/wbc/hand_command',
                 hand_topic='/hal/hand_joint_state',
                 head_topic='/hal/neck_state',
                 head_cmd_topic='/wbc/head_command',
                 waist_topic='/hal/waist_state',
                 waist_cmd_topic='/wbc/waist_command',
                 wheel_cmd_topic='/mbc/wheel_command',
                 wbc_cmd_topic='/wbc/model_predict'):
        self.node_ = agibotdds.Node("A2DRosRobot")
        self.qos = agibotdds.qos(depth=500)
        
        self.head_joint_names = [
            'joint_head_yaw', 'joint_head_pitch'
        ]
        self.waist_joint_names = [
            'joint_body_pitch', 'joint_lift_body'
        ]

        self._body_pose_joint_states = [None, None, None, None]
        self.arm_joint_names = [
            'Joint1_l', 'Joint2_l', 'Joint3_l', 'Joint4_l', 'Joint5_l', 'Joint6_l', 'Joint7_l',
            'Joint1_r', 'Joint2_r', 'Joint3_r', 'Joint4_r', 'Joint5_r', 'Joint6_r', 'Joint7_r'
        ]
        self.hand_joint_names = [
            # Left
            'left_thumb_flex', 'left_index_flex', 'left_middle_flex',
            'left_ring_flex', 'left_little_flex', 'left_thumb_rot',
            # Right
            'right_thumb_flex', 'right_index_flex', 'right_middle_flex',
            'right_ring_flex', 'right_little_flex', 'right_thumb_rot'
        ]
        self.waist_initial_joint_position = [0.523, 0.279]
        self.head_initial_joint_position = [0.000, 0.506]
        self.arm_initial_joint_position = [
            0.881, 0.671, -0.5369, -1.8702, 0.8555, 0.6907, 1.1398,
            -0.881, -0.671, 0.5369, 1.8702, -0.8555, -0.6907, -1.1398
        ]
        self.hand_initial_joint_position = [
            0.881, 0.671, -0.5369, -1.8702, 0.8555, 0.6907, 
            -0.881, -0.671, 0.5369, 1.8702, -0.8555, -0.6907,
        ]
        self._arm_force_states = [
            0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0
        ]

        self._arm_joint_states = JointState()
        self._arm_joint_states.name.extend(self.arm_joint_names)

        self._gripper_joint_states = JointState()
        self._gripper_joint_states.name.extend(['left', 'right'])

        self._hand_joint_states = JointState()
        self._hand_joint_states.name.extend(['left', 'right'])

        self._waist_command = JointState()
        self._waist_command.name.extend([
            'joint_body_pitch', 'joint_lift_body'
        ])
        self._waist_joint_state = WaistState()
        self._waist_joint_state.name.extend([
            'joint_body_pitch', 'joint_lift_body'
        ])

        self._head_joint_state = HeadState()
        self._head_joint_state.name.extend([
            'joint_head_yaw', 'joint_head_pitch'
        ])
        self._gripper2hand_config = {
                "thumb_flex": {"min": 3500.0, "max": 250.0},
                "index_flex": {"min": 17000.0,"max": 10000.0},
                "middle_flex": {"min": 17000.0,"max": 10000.0},
                "ring_flex": {"min": 17000.0,"max": 10000.0},
                "little_flex": {"min": 17000.0,"max": 10000.0},
                "thumb_rot": 4000.0
            }
        
        self._arm_subscriber = self.node_.create_subscriber(
            arm_topic, JointState, self.qos, self.arm_joint_state_callback
        )
        self._arm_buffer = GenericBuffer(capacity=500)
        self._arm_cmd_publisher = self.node_.create_publisher(
            arm_cmd_topic, JointState, self.qos
        )
        self._left_arm_force_subscriber = self.node_.create_subscriber(
            left_arm_force_topic, ArmState, self.qos, self.left_arm_force_callback
        )
        self._left_arm_force_buffer = GenericBuffer(capacity=500)
        self._right_arm_force_subscriber = self.node_.create_subscriber(
            right_arm_force_topic, ArmState, self.qos, self.right_arm_force_callback
        )
        self._right_arm_force_buffer = GenericBuffer(capacity=500)
        self._gripper_subscriber = self.node_.create_subscriber(
            gripper_topic, JointState, self.qos, self.gripper_joint_state_callback
        )
        self._gripper_buffer = GenericBuffer(capacity=500)
        self._gripper_cmd_publisher = self.node_.create_publisher(
            gripper_cmd_topic, JointState, self.qos
        )
        self._hand_subscriber = self.node_.create_subscriber(
            hand_topic, JointState, self.qos, self.hand_joint_state_callback
        )
        self._hand_cmd_publisher = self.node_.create_publisher(
            hand_cmd_topic, JointState, self.qos
        )
        self._hand_buffer = GenericBuffer(capacity=500)
        self._head_subscriber = self.node_.create_subscriber(
            head_topic, HeadState, self.qos, self.head_joint_state_callback
        )
        self._head_buffer = GenericBuffer(capacity=500)
        self._head_cmd_publisher = self.node_.create_publisher(
            head_cmd_topic, JointState, self.qos
        )
        self._waist_subscriber = self.node_.create_subscriber(
            waist_topic, WaistState, self.qos, self.waist_joint_state_callback
        )
        self._waist_buffer = GenericBuffer(capacity=500)
        self._waist_cmd_publisher = self.node_.create_publisher(
            waist_cmd_topic, JointState, self.qos
        )
        self._wheel_cmd_publisher = self.node_.create_publisher(
            wheel_cmd_topic, TwistStamped, self.qos
        )

        self._wbc_cmd_publisher = self.node_.create_publisher(
            wbc_cmd_topic, ModelPredict, self.qos
        )

        self._body_pose_client = self.node_.create_client(
            '/wbc/body_pose', BodyPoseRequest, BodyPoseResponse
        )



    def head_joint_state_callback(self, msg):
        del self._head_joint_state.motor_states[:]
        self._head_joint_state.motor_states.extend(msg.motor_states)
        self._body_pose_joint_states[0] = msg.motor_states[0].position * 180 / np.pi
        self._body_pose_joint_states[1] = msg.motor_states[1].position * 180 / np.pi
        self._head_buffer.write(self._body_pose_joint_states[:2], timestamp=int(msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec))

    def head_joint_states_nearest(self, timestamp_ns):
        return self._head_buffer.read_nearest(timestamp_ns)
    
    def arm_joint_states_nearest(self, timestamp_ns):
        return self._arm_buffer.read_nearest(timestamp_ns)
    
    def waist_joint_states_nearest(self, timestamp_ns):
        return self._waist_buffer.read_nearest(timestamp_ns)
    
    def gripper_joint_states_nearest(self, timestamp_ns):
        return self._gripper_buffer.read_nearest(timestamp_ns)
    
    def waist_joint_state_callback(self, msg):
        del self._waist_joint_state.motor_states[:]
        self._waist_joint_state.motor_states.extend(msg.motor_states)
        for i in range(len(msg.motor_states)):
            if msg.name[i] == "joint_body_pitch":
                self._body_pose_joint_states[2] = msg.motor_states[i].position
            elif msg.name[i] == "joint_lift_body":
                self._body_pose_joint_states[3] = msg.motor_states[i].position
        self._waist_buffer.write(self._body_pose_joint_states[2:4], timestamp=int(msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec))
        if len(msg.motor_states) < 2:
            hal_logger.warning(f"Waist has less than 2 motors, {msg}")

    def gripper_joint_state_callback(self, msg):
        self._gripper_joint_states.position[:] = []
        self._gripper_joint_states.position.extend(msg.position)
        self._gripper_joint_states.velocity[:] = []
        self._gripper_joint_states.velocity.extend(msg.velocity)
        self._gripper_joint_states.effort[:] = []
        self._gripper_joint_states.effort.extend(msg.effort)
        self._gripper_joint_states.header.stamp.sec = msg.header.stamp.sec
        self._gripper_joint_states.header.stamp.nanosec = msg.header.stamp.nanosec
        self._gripper_buffer.write(list(msg.position), timestamp=int(msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec))
    def arm_joint_state_callback(self, msg):
        self._arm_joint_states.position[:] = []
        self._arm_joint_states.velocity[:] = []
        self._arm_joint_states.effort[:] = []
        self._arm_joint_states.position.extend(msg.position)
        self._arm_joint_states.velocity.extend(msg.velocity)
        self._arm_joint_states.effort.extend(msg.effort)
        self._arm_joint_states.header.stamp.sec = msg.header.stamp.sec
        self._arm_joint_states.header.stamp.nanosec = msg.header.stamp.nanosec
        self._arm_buffer.write(list(msg.position), timestamp=int(msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec))
    
    def left_arm_force_callback(self, msg):
        self._arm_force_states[0:6] = msg.force_data
        self._left_arm_force_buffer.write(self._arm_force_states[:6], timestamp=int(msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec))
    
    def right_arm_force_callback(self, msg):
        self._arm_force_states[6:12] = msg.force_data
        self._right_arm_force_buffer.write(self._arm_force_states[6:12], timestamp=int(msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec))
    
    def hand_joint_state_callback(self, msg):
        self._hand_joint_states.position[:] = []
        self._hand_joint_states.velocity[:] = []
        self._hand_joint_states.effort[:] = []
        self._hand_joint_states.position.extend(msg.position)
        self._hand_joint_states.velocity.extend(msg.velocity)
        self._hand_joint_states.effort.extend(msg.effort)
        self._hand_joint_states.header.stamp.sec = msg.header.stamp.sec
        self._hand_joint_states.header.stamp.nanosec = msg.header.stamp.nanosec
        self._hand_buffer.write(list(msg.position), timestamp=int(msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec))
    
    def body_pose_joint_states(self):
        return self._body_pose_joint_states

    def head_joint_states(self):
        return self._body_pose_joint_states[:2], self._head_joint_state.header.stamp.sec * 1e9 + self._head_joint_state.header.stamp.nanosec

    def waist_joint_states(self):
        return self._body_pose_joint_states[2:4], self._waist_joint_state.header.stamp.sec * 1e9 + self._waist_joint_state.header.stamp.nanosec

    def arm_joint_states(self):
        return self._arm_joint_states.position, self._arm_joint_states.header.stamp.sec * 1e9 + self._arm_joint_states.header.stamp.nanosec

    def gripper_states(self):
        return self._gripper_joint_states.position, self._gripper_joint_states.header.stamp.sec * 1e9 + self._gripper_joint_states.header.stamp.nanosec

    def hand_joint_states(self):
        return self._hand_joint_states.position, self._hand_joint_states.header.stamp.sec * 1e9 + self._hand_joint_states.header.stamp.nanosec
    
    def hand_joint_states_nearest(self, timestamp_ns):
        return self._hand_buffer.read_nearest(timestamp_ns)

    def hand_force_states(self):
        return self._arm_force_states

    def move_wheel(self, linear, angular):
        ts = TwistStamped()
        ts.twist.linear.x = linear
        ts.twist.angular.z = angular
        # hal_logger.info(f"Move Wheel: {ts}")
        self._wheel_cmd_publisher.publish(ts)

    def linear_map(self, x, in_min, in_max, out_min, out_max):
        return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min
    
    def angle2radian(self, angle):
        return angle * np.pi / 180

    def get_hand_positions(self, controller, is_left):
        positions = []

        # 拇指弯曲
        thumb_config = self._gripper2hand_config['thumb_flex']
        pre_position = self.linear_map(
            controller,
            0.0,
            1.0,
            thumb_config['min'],
            thumb_config['max']
        ) / 100.0
        positions.append(self.angle2radian(pre_position))

        # 其他手指
        for finger in ['index_flex', 'middle_flex', 'ring_flex', 'little_flex']:
            finger_config = self._gripper2hand_config[finger]
            pre_position = self.linear_map(
                controller,
                0.0,
                1.0,
                finger_config['min'],
                finger_config['max']
            ) / 100.0
            positions.append(self.angle2radian(pre_position))

        # 拇指旋转
        pre_position = float(self._gripper2hand_config['thumb_rot']) / 100.0
        positions.append(self.angle2radian(pre_position))

        return positions

    def move_hand_as_gripper(self, positions):
        gripper_left = positions[0]
        gripper_right = positions[1]
        position_left = self.get_hand_positions(gripper_left, True)
        position_right = self.get_hand_positions(gripper_right, False)
        position_all = list(position_left + position_right)

        js = JointState()
        js.name.extend(self.hand_joint_names)  # 直接替换
        js.position.extend(position_all)
        # hal_logger.info(f"Move Hand: {js}")
        self._hand_cmd_publisher.publish(js)
    
    def move_hand(self, positions):
        js = JointState()
        js.name.extend(self.hand_joint_names)  # 直接替换
        js.position.extend(positions)
        self._hand_cmd_publisher.publish(js)
    
    # use radian
    def move_head(self, positions, uint_radian=False):
        request = BodyPoseRequest()
        request.joint_states[:] = []
        request.joint_states.extend(self._body_pose_joint_states)  # 直接替换
        request.joint_flag = 3  #设置2进制0011表示设置第1位和第2位
        request.joint_states[0] = positions[0] * (180 / np.pi) #if uint_radian else positions[0]
        request.joint_states[1] = positions[1] * (180 / np.pi) #if uint_radian else positions[1]
        # hal_logger.info(f"Move Head: {request}")
        response = self._body_pose_client.send_request(request)

    # use radian
    def move_waist(self, positions, uint_radian=False):
        request = BodyPoseRequest()
        request.joint_states[:] = []
        request.joint_states.extend(self._body_pose_joint_states)  # 直接替换
        request.joint_flag = 12  #设置2进制1100表示设置第3位和第4位
        request.joint_states[2] = positions[0] * (180 / np.pi) #if uint_radian else positions[0]
        request.joint_states[3] = positions[1] # cm
        # hal_logger.info(f"Move Waist: {request}")
        response = self._body_pose_client.send_request(request)

    # use radian, cm
    def move_head_and_waist(self, head_positions, waist_positions):
        request = BodyPoseRequest()
        request.joint_states[:] = []
        request.joint_states.extend(self._body_pose_joint_states)  # 直接替换
        request.joint_flag = 15  #设置2进制1111表示设置第1位、第2位、第3位和第4位
        request.joint_states[0] = head_positions[0] * (180 / np.pi)
        request.joint_states[1] = head_positions[1] * (180 / np.pi)
        request.joint_states[2] = waist_positions[0] * (180 / np.pi)
        request.joint_states[3] = waist_positions[1] # cm
        # hal_logger.info(f"Move Head and Waist: {request}")
        response = self._body_pose_client.send_request(request)

    def move_gripper(self, positions):
        js = JointState()
        js.name.extend(['left', 'right'])  # 直接替换
        js.position.extend(positions)
        if positions[0] > 1.0:
            js.position[0] = (positions[0] - 35.0) / (120.0 - 35.0)
        if positions[1] > 1.0:
            js.position[1] = (positions[1] - 35.0) / (120.0 - 35.0)
        self._gripper_cmd_publisher.publish(js)
        # hal_logger.info(f"Move Gripper: {js}")
        return self._gripper_joint_states

    def move_arm(self, positions):
        js = JointState()
        js.name.extend(self.arm_joint_names)  # 直接替换
        js.position.extend(positions)
        self._arm_cmd_publisher.publish(js)
        # hal_logger.info(f"Move Arm: {js}")

    def move_arm_to_target_pose(self,
                                infer_timestamp: int,
                                head_joint_states: list,
                                waist_joint_states: list,
                                arm_joint_states: list,
                                actions: list,
                                is_delta: bool,
                                model_sleep_time: float,
                                trajectory_reference_time: float):
        def xyzrpy_to_xyzquat(xyzrpy):
            xyz = xyzrpy[:3]
            quat = R.from_euler("xyz", xyzrpy[3:]).as_quat()
            quat = np.array(quat)
            xyzquat = np.concatenate([xyz, quat])
            return xyzquat
        
        if is_delta:
            wbc_cmd = ModelPredict()
            wbc_cmd.header.stamp.sec = infer_timestamp // 1000000000
            wbc_cmd.header.stamp.nanosec = infer_timestamp % 1000000000
            wbc_cmd.header.frame_id = "base_link"
            # 注意顺序
            names = [self.waist_joint_names[1], self.waist_joint_names[0]] + self.head_joint_names + self.arm_joint_names
            wbc_cmd.body_joint_names.extend(names)
            body_joint_positions = [waist_joint_states[1], waist_joint_states[0]] +  head_joint_states + arm_joint_states
            wbc_cmd.body_joint_positions.extend(body_joint_positions)
            wbc_cmd.model_output_type = 0
            assert len(actions) == 30
            for action in actions:
                left_ee_xyzquat = xyzrpy_to_xyzquat(action[:6])
                right_ee_xyzquat = xyzrpy_to_xyzquat(action[6:12])
                wbc_cmd.target_poses.append(Pose())
                wbc_cmd.target_poses[-1].position.x = left_ee_xyzquat[0]
                wbc_cmd.target_poses[-1].position.y = left_ee_xyzquat[1]
                wbc_cmd.target_poses[-1].position.z = left_ee_xyzquat[2]
                wbc_cmd.target_poses[-1].orientation.x = left_ee_xyzquat[3]
                wbc_cmd.target_poses[-1].orientation.y = left_ee_xyzquat[4]
                wbc_cmd.target_poses[-1].orientation.z = left_ee_xyzquat[5]
                wbc_cmd.target_poses[-1].orientation.w = left_ee_xyzquat[6]
                wbc_cmd.target_poses.append(Pose())
                wbc_cmd.target_poses[-1].position.x = right_ee_xyzquat[0]
                wbc_cmd.target_poses[-1].position.y = right_ee_xyzquat[1]
                wbc_cmd.target_poses[-1].position.z = right_ee_xyzquat[2]
                wbc_cmd.target_poses[-1].orientation.x = right_ee_xyzquat[3]
                wbc_cmd.target_poses[-1].orientation.y = right_ee_xyzquat[4]
                wbc_cmd.target_poses[-1].orientation.z = right_ee_xyzquat[5]
                wbc_cmd.target_poses[-1].orientation.w = right_ee_xyzquat[6]
            wbc_cmd.model_sleep_time = model_sleep_time
            wbc_cmd.trajectory_reference_time = trajectory_reference_time
            # hal_logger.info(f"Move Arm to Target Pose: {wbc_cmd}")
            self._wbc_cmd_publisher.publish(wbc_cmd)
        else:
            raise Exception("Not implemented")


    def reset(self, arm_positions=None, gripper_positions=None, hand_positions=None, waist_positions=None, head_positions=None):
        timeout = 10
        while len(self._arm_joint_states.position) == 0 and timeout > 0:
            time.sleep(0.1)
            timeout -= 1

        if len(self._arm_joint_states.position) == 0:
            raise Exception("Arm joint states not ready, reset failed")

        qpos = list(self._arm_joint_states.position)

        arm_initial_joint_position = arm_positions[:14] if arm_positions is not None else self.arm_initial_joint_position
             
        dof = 14
        interval = 0.01
        rk = ruckig.Ruckig(dof, interval)
        rk_input = ruckig.InputParameter(dof)
        rk_output = ruckig.OutputParameter(dof)
        rk_input.current_position = qpos
        rk_input.current_velocity = [0.0] * 14
        rk_input.current_acceleration = [0.0] * 14

        rk_input.target_position = arm_initial_joint_position
        rk_input.target_velocity = [0.0] * 14
        rk_input.target_acceleration = [0.0] * 14

        rk_input.max_velocity = [2.0] * 14
        rk_input.max_acceleration = [1.0] * 14
        rk_input.max_jerk = [5.0] * 14

        trajs = []
        while rk.update(rk_input, rk_output) == ruckig.Result.Working:
            trajs.append(rk_output.new_position)
            rk_output.pass_to_input(rk_input)
        for traj in trajs:      
            self.move_arm(traj)
            time.sleep(interval)

        self.move_gripper([0.0, 0.0] if gripper_positions is None else gripper_positions)
        self.move_hand( self.hand_initial_joint_position if hand_positions is None else hand_positions)
        self.move_waist(self.waist_initial_joint_position if waist_positions is None else waist_positions)
        self.move_head(self.head_initial_joint_position if head_positions is None else head_positions)

    def shutdown(self):
        agibotdds.shutdown()
